

server:
  port: 8009

spring:
  profiles:
    active: dev
  application:
    name: bff-service
  cloud:
    sentinel:
      transport:
        dashboard: localhost:8686
        port: 8719
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      connection-test-query: select 1
      connection-timeout: 20000
      idle-timeout: 300000
      maximum-pool-size: 5
      minimum-idle: 5

congomall:
  fastjson:
    safa-mode: true
  swagger:
    contact:
      name: chen.ma
    description: BFF Service
    title: BFF Service
    version: 1.0.0
  bff:
    qps-count: 1

jetcache:
  areaInCacheName: false
  local:
    default:
      type: linkedhashmap
      keyConvertor: fastjson2
  remote:
    default:
      type: redis.springdata
      keyConvertor: fastjson2
      keyPrefix: 'congomall-bff:'
      valueEncoder: KRYO5
      valueDecoder: KRYO5
      defaultExpireInMillis: 5000

feign:
  client:
    config:
      default:
        connectTimeout: 5000
        loggerLevel: HEADERS
        readTimeout: 5000
  httpclient:
    enabled: false
  okhttp:
    enabled: true

mybatis-plus:
  global-config:
    db-config:
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0
