<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <title>刚果商城</title>
  <meta name="keywords" content="刚果商城,刚果商城开源购物电商商城, JavaEdge">
  <meta name="description" content="刚果商城开源电商商城 作者 JavaEdge">
  <meta http-equiv="X-UA-Compatible" content="IE=Edge">
  <meta name="wap-font-scale" content="no">
  <!-- <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1"> -->
  <link rel="shortcut icon " type="images/x-icon" href="/static/images/C.png">
</head>
<!-- <script src="https://cdn.bootcss.com/vue/2.5.3/vue.min.js"></script>
<script src="https://cdn.bootcss.com/vue-router/3.0.1/vue-router.min.js"></script>
<script src="https://cdn.bootcss.com/vuex/3.0.1/vuex.min.js"></script>
<script src="https://cdn.bootcss.com/axios/0.17.1/axios.min.js"></script> -->
<script>
  (function (h, o, t, j, a, r) {
    h.hj = h.hj || function () { (h.hj.q = h.hj.q || []).push(arguments) }
    h._hjSettings = { hjid: 695959, hjsv: 6 }
    a = o.getElementsByTagName('head')[0]
    r = o.createElement('script')
    r.async = 1
    r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv
    a.appendChild(r)
  })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=')

  // 百度统计
  var _hmt = _hmt || [];
  (function () {
    var hm = document.createElement('script')
    hm.src = 'https://hm.baidu.com/hm.js?bbbf583bcba8f9bae1097a1198d5c182'
    var s = document.getElementsByTagName('script')[0]
    s.parentNode.insertBefore(hm, s)
  })()
</script>
</script>

<body>
  <div id="app"></div>
  <!-- built files will be auto injected -->
</body>

</html>
<style>
  body{
	min-width:1250px;
  }
</style>
