## DDD-Mall-Front

### 基于 Vue 开发的 CMall 商城前台页面

### 项目已部署，在线 Demo

- 前台商城：[http://cmall.magestack.cn](http://cmall.magestack.cn)

感谢 [Exrick](https://github.com/Exrick) 的开源 [xmall-front](https://github.com/Exrick/xmall-front) 项目提供前端页面及框架支持。

后端全部重新开发接口，实现 C 端商城系统，后端接口项目请跳转至 [congomall](https://gitee.com/opengoofy/congomall) 项目仓库查看。

### 所用技术

- Vue 2.x
- Vuex
- Vue Router
- Element UI
- ES6
- webpack
- axios
- Node.js
- 第三方 SDK
  - 极验验证码移除文档
- 第三方插件
  - [hotjar](https://github.com/Exrick/xmall/blob/master/study/hotjar.md)：一体化分析和反馈
  - [Gitment](https://github.com/imsun/gitment)

### 技术疑问交流

- [添加交流群](https://magestack.cn/preparation/group.html)