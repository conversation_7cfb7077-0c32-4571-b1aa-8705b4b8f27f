{"name": "congomall-front", "version": "1.0.0", "description": "A Vue.js project", "author": "二次开发:  <PERSON><PERSON><PERSON>, 原作者: <PERSON><PERSON> <<EMAIL>>", "private": true, "scripts": {"dev": "node build/dev-server.js", "start": "node build/dev-server.js", "build": "node build/build.js", "lint": "eslint --ext .js,.vue src"}, "dependencies": {"axios": "^0.16.2", "element-ui": "^1.4.13", "geetest3": "^1.0.0", "gitment": "0.0.3", "vue": "^2.5.16", "vue-content-placeholders": "^0.2.1", "vue-cookie": "^1.1.4", "vue-cropper": "^0.1.7", "vue-infinite-scroll": "^2.0.2", "vue-lazyload": "^1.2.2", "vue-router": "^2.8.1", "vuex": "^2.5.0"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-core": "^6.22.1", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.10", "babel-plugin-component": "^0.10.1", "babel-plugin-transform-runtime": "^6.22.0", "babel-preset-env": "^1.6.1", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.5.0", "copy-webpack-plugin": "^4.5.1", "css-loader": "^0.28.11", "element-theme-default": "^1.4.13", "eslint": "^3.19.0", "eslint-config-standard": "^6.2.1", "eslint-friendly-formatter": "^2.0.7", "eslint-loader": "^1.7.1", "eslint-plugin-html": "^2.0.0", "eslint-plugin-promise": "^3.7.0", "eslint-plugin-standard": "^2.0.1", "eventsource-polyfill": "^0.9.6", "express": "^4.16.3", "extract-text-webpack-plugin": "^2.0.0", "file-loader": "^0.11.1", "friendly-errors-webpack-plugin": "^1.7.0", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.17.3", "node-sass": "^4.8.3", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^1.4.0", "rimraf": "^2.6.2", "sass-loader": "^6.0.7", "semver": "^5.5.0", "shelljs": "^0.7.6", "url-loader": "^0.5.8", "vue-loader": "^12.1.0", "vue-style-loader": "^3.1.2", "vue-template-compiler": "^2.5.16", "webpack": "^2.6.1", "webpack-bundle-analyzer": "^2.11.1", "webpack-dev-middleware": "^1.12.2", "webpack-hot-middleware": "^2.22.0", "webpack-merge": "^4.1.2"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}