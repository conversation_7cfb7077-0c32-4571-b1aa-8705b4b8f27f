spring:
  cloud:
    nacos:
      discovery:
        server-addr: **********:8848
    stream:
      rocketmq:
        binder:
          name-server: 127.0.0.1:9876
  mail:
    default-encoding: UTF-8
    host: smtp.163.com
    password: XERZPMCFAIDMBXNY
    port: 25
    protocol: smtp
    username: congo<PERSON><EMAIL>
  redis:
    host: localhost
    port: 6379
    # password: 123456
  shardingsphere:
    datasource:
      ds-0:
        username: root
        password: 123456
        jdbc-url: ******************************************************************************************************************************************************************************************************

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
