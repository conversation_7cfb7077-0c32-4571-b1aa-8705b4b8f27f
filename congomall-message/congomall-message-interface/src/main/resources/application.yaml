server:
  port: 8001

spring:
  profiles:
    active: dev
  application:
    name: message-service
  cloud:
    sentinel:
      transport:
        dashboard: localhost:8686
        port: 8719
    stream:
      bindings:
        mailSend:
          consumer:
            concurrency: 4
            max-attempts: 1
          content-type: application/json
          destination: message-center_topic
          group: message-center_mail-send_cg
        messageOutput:
          content-type: application/json
          destination: message-center_topic
          group: message-center_general-send_pg
      rocketmq:
        bindings:
          mailSend:
            consumer:
              delay-level-when-next-consume: -1
              tags: common_message-center_mail-send_tag
  shardingsphere:
    datasource:
      ds-0:
        driver-class-name: com.mysql.jdbc.Driver
        type: com.zaxxer.hikari.HikariDataSource
      names: ds-0
    props:
      sql-show: true
    rules:
      sharding:
        sharding-algorithms:
          snowflake_date_algorithm:
            props:
              algorithmClassName: org.opengoofy.congomall.biz.message.infrastructure.algorithm.SnowflakeDateShardingAlgorithm
              strategy: complex
            type: CLASS_BASED
        tables:
          send_record:
            actual-data-nodes: ds-0.send_record_$->{2023..2026}_m$->{1..12}
            table-strategy:
              complex:
                sharding-algorithm-name: snowflake_date_algorithm
                sharding-columns: create_time,msg_id
          send_record_extend:
            actual-data-nodes: ds-0.send_record_extend_$->{2023..2026}_m$->{1..12}
            table-strategy:
              complex:
                sharding-algorithm-name: snowflake_date_algorithm
                sharding-columns: create_time,msg_id

congomall:
  fastjson:
    safa-mode: true
  swagger:
    contact:
      name: chen.ma
    description: Message Service
    title: Message Service
    version: 1.0.0
  cache:
    redis:
      prefix: ''
      value-timeout: 50000

mybatis-plus:
  global-config:
    db-config:
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0
