

server:
  port: 8004

spring:
  profiles:
    active: dev
  application:
    name: product-service
  cloud:
    sentinel:
      transport:
        dashboard: localhost:8686
        port: 8719
  shardingsphere:
    datasource:
      ds-0:
        driver-class-name: com.mysql.jdbc.Driver
        type: com.zaxxer.hikari.HikariDataSource
      names: ds-0
    props:
      sql-show: true
    rules:
      sharding:
        sharding-algorithms:
          product_sharding_by_mod_algorithm:
            props:
              algorithmClassName: org.opengoofy.congomall.biz.product.infrastructure.algorithm.ProductSnowflakeServiceShardingAlgorithm
              sharding-count: 16
              strategy: complex
            type: CLASS_BASED
          product-sku_sharding_by_mod_algorithm:
            props:
              algorithmClassName: org.opengoofy.congomall.biz.product.infrastructure.algorithm.ProductSkuSnowflakeServiceShardingAlgorithm
              sharding-count: 16
              strategy: complex
            type: CLASS_BASED
          product-comment_sharding_by_mod:
            props:
              sharding-count: 16
            type: HASH_MOD
        tables:
          product_spu:
            actual-data-nodes: ds-0.product_spu_$->{0..15}
            table-strategy:
              complex:
                sharding-algorithm-name: product_sharding_by_mod_algorithm
                sharding-columns: brand_id,id
          product_sku:
            actual-data-nodes: ds-0.product_sku_$->{0..15}
            table-strategy:
              complex:
                sharding-algorithm-name: product-sku_sharding_by_mod_algorithm
                sharding-columns: brand_id,product_id
          product_comment:
            actual-data-nodes: ds-0.product_comment_$->{0..15}
            table-strategy:
              standard:
                sharding-algorithm-name: product-comment_sharding_by_mod
                sharding-column: product_id

  dynamic:
    thread-pool:
      default-executor:
        active-alarm: 80
        alarm: false
        allow-core-thread-time-out: true
        blocking-queue: ResizableCapacityLinkedBlockingQueue
        capacity-alarm: 80
        core-pool-size: 20
        execute-time-out: 100
        keep-alive-time: 9999
        maximum-pool-size: 40
        queue-capacity: 4096
        rejected-handler: AbortPolicy

congomall:
  fastjson:
    safa-mode: true
  swagger:
    contact:
      name: chen.ma
    description: Product Service
    title: Product Service
    version: 1.0.0
  cache:
    redis:
      prefix: '${spring.application.name}:'
      value-timeout: 50000

mybatis-plus:
  global-config:
    db-config:
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath:mapper/*.xml

seata:
  application-id: ${spring.application.name}
  enable-auto-data-source-proxy: false
  service:
    grouplist:
      seata-server: 127.0.0.1:8091
    vgroup-mapping:
      my-tx-group: seata-server
  tx-service-group: my-tx-group
  use-jdk-proxy: true