use congomall_customer_user;

CREATE TABLE `customer_user_0`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name`           varchar(256) DEFAULT NULL COMMENT '昵称',
    `account_number` varchar(256) DEFAULT NULL COMMENT '账号',
    `password`       varchar(256) DEFAULT NULL COMMENT '密码',
    `phone`          varchar(128) DEFAULT NULL COMMENT '手机号',
    `mail`           varchar(128) DEFAULT NULL COMMENT '邮箱',
    `age`            int(3) DEFAULT NULL COMMENT '年龄',
    `gender`         tinyint(1) DEFAULT NULL COMMENT '性别',
    `avatar`         varchar(256) DEFAULT NULL COMMENT '头像',
    `create_time`    datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`    datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`       tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1634554535496892417 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户';

CREATE TABLE `customer_user_1`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name`           varchar(256) DEFAULT NULL COMMENT '昵称',
    `account_number` varchar(256) DEFAULT NULL COMMENT '账号',
    `password`       varchar(256) DEFAULT NULL COMMENT '密码',
    `phone`          varchar(128) DEFAULT NULL COMMENT '手机号',
    `mail`           varchar(128) DEFAULT NULL COMMENT '邮箱',
    `age`            int(3) DEFAULT NULL COMMENT '年龄',
    `gender`         tinyint(1) DEFAULT NULL COMMENT '性别',
    `avatar`         varchar(256) DEFAULT NULL COMMENT '头像',
    `create_time`    datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`    datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`       tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1634929763204202497 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户';

CREATE TABLE `customer_user_10`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name`           varchar(256) DEFAULT NULL COMMENT '昵称',
    `account_number` varchar(256) DEFAULT NULL COMMENT '账号',
    `password`       varchar(256) DEFAULT NULL COMMENT '密码',
    `phone`          varchar(128) DEFAULT NULL COMMENT '手机号',
    `mail`           varchar(128) DEFAULT NULL COMMENT '邮箱',
    `age`            int(3) DEFAULT NULL COMMENT '年龄',
    `gender`         tinyint(1) DEFAULT NULL COMMENT '性别',
    `avatar`         varchar(256) DEFAULT NULL COMMENT '头像',
    `create_time`    datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`    datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`       tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1634554578039717889 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户';

CREATE TABLE `customer_user_11`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name`           varchar(256) DEFAULT NULL COMMENT '昵称',
    `account_number` varchar(256) DEFAULT NULL COMMENT '账号',
    `password`       varchar(256) DEFAULT NULL COMMENT '密码',
    `phone`          varchar(128) DEFAULT NULL COMMENT '手机号',
    `mail`           varchar(128) DEFAULT NULL COMMENT '邮箱',
    `age`            int(3) DEFAULT NULL COMMENT '年龄',
    `gender`         tinyint(1) DEFAULT NULL COMMENT '性别',
    `avatar`         varchar(256) DEFAULT NULL COMMENT '头像',
    `create_time`    datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`    datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`       tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1634554580677935105 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户';

CREATE TABLE `customer_user_12`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name`           varchar(256) DEFAULT NULL COMMENT '昵称',
    `account_number` varchar(256) DEFAULT NULL COMMENT '账号',
    `password`       varchar(256) DEFAULT NULL COMMENT '密码',
    `phone`          varchar(128) DEFAULT NULL COMMENT '手机号',
    `mail`           varchar(128) DEFAULT NULL COMMENT '邮箱',
    `age`            int(3) DEFAULT NULL COMMENT '年龄',
    `gender`         tinyint(1) DEFAULT NULL COMMENT '性别',
    `avatar`         varchar(256) DEFAULT NULL COMMENT '头像',
    `create_time`    datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`    datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`       tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1634554585421692929 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户';

CREATE TABLE `customer_user_13`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name`           varchar(256) DEFAULT NULL COMMENT '昵称',
    `account_number` varchar(256) DEFAULT NULL COMMENT '账号',
    `password`       varchar(256) DEFAULT NULL COMMENT '密码',
    `phone`          varchar(128) DEFAULT NULL COMMENT '手机号',
    `mail`           varchar(128) DEFAULT NULL COMMENT '邮箱',
    `age`            int(3) DEFAULT NULL COMMENT '年龄',
    `gender`         tinyint(1) DEFAULT NULL COMMENT '性别',
    `avatar`         varchar(256) DEFAULT NULL COMMENT '头像',
    `create_time`    datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`    datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`       tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1634554589880238081 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户';

CREATE TABLE `customer_user_14`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name`           varchar(256) DEFAULT NULL COMMENT '昵称',
    `account_number` varchar(256) DEFAULT NULL COMMENT '账号',
    `password`       varchar(256) DEFAULT NULL COMMENT '密码',
    `phone`          varchar(128) DEFAULT NULL COMMENT '手机号',
    `mail`           varchar(128) DEFAULT NULL COMMENT '邮箱',
    `age`            int(3) DEFAULT NULL COMMENT '年龄',
    `gender`         tinyint(1) DEFAULT NULL COMMENT '性别',
    `avatar`         varchar(256) DEFAULT NULL COMMENT '头像',
    `create_time`    datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`    datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`       tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1634554594259091457 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户';

CREATE TABLE `customer_user_15`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name`           varchar(256) DEFAULT NULL COMMENT '昵称',
    `account_number` varchar(256) DEFAULT NULL COMMENT '账号',
    `password`       varchar(256) DEFAULT NULL COMMENT '密码',
    `phone`          varchar(128) DEFAULT NULL COMMENT '手机号',
    `mail`           varchar(128) DEFAULT NULL COMMENT '邮箱',
    `age`            int(3) DEFAULT NULL COMMENT '年龄',
    `gender`         tinyint(1) DEFAULT NULL COMMENT '性别',
    `avatar`         varchar(256) DEFAULT NULL COMMENT '头像',
    `create_time`    datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`    datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`       tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1634554598784745473 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户';

CREATE TABLE `customer_user_2`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name`           varchar(256) DEFAULT NULL COMMENT '昵称',
    `account_number` varchar(256) DEFAULT NULL COMMENT '账号',
    `password`       varchar(256) DEFAULT NULL COMMENT '密码',
    `phone`          varchar(128) DEFAULT NULL COMMENT '手机号',
    `mail`           varchar(128) DEFAULT NULL COMMENT '邮箱',
    `age`            int(3) DEFAULT NULL COMMENT '年龄',
    `gender`         tinyint(1) DEFAULT NULL COMMENT '性别',
    `avatar`         varchar(256) DEFAULT NULL COMMENT '头像',
    `create_time`    datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`    datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`       tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1634554596293328897 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户';

CREATE TABLE `customer_user_3`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name`           varchar(256) DEFAULT NULL COMMENT '昵称',
    `account_number` varchar(256) DEFAULT NULL COMMENT '账号',
    `password`       varchar(256) DEFAULT NULL COMMENT '密码',
    `phone`          varchar(128) DEFAULT NULL COMMENT '手机号',
    `mail`           varchar(128) DEFAULT NULL COMMENT '邮箱',
    `age`            int(3) DEFAULT NULL COMMENT '年龄',
    `gender`         tinyint(1) DEFAULT NULL COMMENT '性别',
    `avatar`         varchar(256) DEFAULT NULL COMMENT '头像',
    `create_time`    datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`    datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`       tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1634929834410901505 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户';

CREATE TABLE `customer_user_4`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name`           varchar(256) DEFAULT NULL COMMENT '昵称',
    `account_number` varchar(256) DEFAULT NULL COMMENT '账号',
    `password`       varchar(256) DEFAULT NULL COMMENT '密码',
    `phone`          varchar(128) DEFAULT NULL COMMENT '手机号',
    `mail`           varchar(128) DEFAULT NULL COMMENT '邮箱',
    `age`            int(3) DEFAULT NULL COMMENT '年龄',
    `gender`         tinyint(1) DEFAULT NULL COMMENT '性别',
    `avatar`         varchar(256) DEFAULT NULL COMMENT '头像',
    `create_time`    datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`    datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`       tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1634554587552399361 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户';

CREATE TABLE `customer_user_5`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name`           varchar(256) DEFAULT NULL COMMENT '昵称',
    `account_number` varchar(256) DEFAULT NULL COMMENT '账号',
    `password`       varchar(256) DEFAULT NULL COMMENT '密码',
    `phone`          varchar(128) DEFAULT NULL COMMENT '手机号',
    `mail`           varchar(128) DEFAULT NULL COMMENT '邮箱',
    `age`            int(3) DEFAULT NULL COMMENT '年龄',
    `gender`         tinyint(1) DEFAULT NULL COMMENT '性别',
    `avatar`         varchar(256) DEFAULT NULL COMMENT '头像',
    `create_time`    datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`    datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`       tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1634554582691201025 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户';

CREATE TABLE `customer_user_6`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name`           varchar(256) DEFAULT NULL COMMENT '昵称',
    `account_number` varchar(256) DEFAULT NULL COMMENT '账号',
    `password`       varchar(256) DEFAULT NULL COMMENT '密码',
    `phone`          varchar(128) DEFAULT NULL COMMENT '手机号',
    `mail`           varchar(128) DEFAULT NULL COMMENT '邮箱',
    `age`            int(3) DEFAULT NULL COMMENT '年龄',
    `gender`         tinyint(1) DEFAULT NULL COMMENT '性别',
    `avatar`         varchar(256) DEFAULT NULL COMMENT '头像',
    `create_time`    datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`    datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`       tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1634554560033570817 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户';

CREATE TABLE `customer_user_7`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name`           varchar(256) DEFAULT NULL COMMENT '昵称',
    `account_number` varchar(256) DEFAULT NULL COMMENT '账号',
    `password`       varchar(256) DEFAULT NULL COMMENT '密码',
    `phone`          varchar(128) DEFAULT NULL COMMENT '手机号',
    `mail`           varchar(128) DEFAULT NULL COMMENT '邮箱',
    `age`            int(3) DEFAULT NULL COMMENT '年龄',
    `gender`         tinyint(1) DEFAULT NULL COMMENT '性别',
    `avatar`         varchar(256) DEFAULT NULL COMMENT '头像',
    `create_time`    datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`    datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`       tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1634554574482948097 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户';

CREATE TABLE `customer_user_8`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name`           varchar(256) DEFAULT NULL COMMENT '昵称',
    `account_number` varchar(256) DEFAULT NULL COMMENT '账号',
    `password`       varchar(256) DEFAULT NULL COMMENT '密码',
    `phone`          varchar(128) DEFAULT NULL COMMENT '手机号',
    `mail`           varchar(128) DEFAULT NULL COMMENT '邮箱',
    `age`            int(3) DEFAULT NULL COMMENT '年龄',
    `gender`         tinyint(1) DEFAULT NULL COMMENT '性别',
    `avatar`         varchar(256) DEFAULT NULL COMMENT '头像',
    `create_time`    datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`    datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`       tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1634554569361702913 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户';

CREATE TABLE `customer_user_9`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name`           varchar(256) DEFAULT NULL COMMENT '昵称',
    `account_number` varchar(256) DEFAULT NULL COMMENT '账号',
    `password`       varchar(256) DEFAULT NULL COMMENT '密码',
    `phone`          varchar(128) DEFAULT NULL COMMENT '手机号',
    `mail`           varchar(128) DEFAULT NULL COMMENT '邮箱',
    `age`            int(3) DEFAULT NULL COMMENT '年龄',
    `gender`         tinyint(1) DEFAULT NULL COMMENT '性别',
    `avatar`         varchar(256) DEFAULT NULL COMMENT '头像',
    `create_time`    datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`    datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`       tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1634554571890868225 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户';

CREATE TABLE `operation_log_0`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `before_content`    varchar(2048) DEFAULT NULL COMMENT '修改前',
    `after_content`     varchar(2048) DEFAULT NULL COMMENT '修改后',
    `operation_content` varchar(2048) DEFAULT NULL COMMENT '修改内容',
    `create_time`       datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户操作日志';

CREATE TABLE `operation_log_1`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `before_content`    varchar(2048) DEFAULT NULL COMMENT '修改前',
    `after_content`     varchar(2048) DEFAULT NULL COMMENT '修改后',
    `operation_content` varchar(2048) DEFAULT NULL COMMENT '修改内容',
    `create_time`       datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户操作日志';

CREATE TABLE `operation_log_10`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `before_content`    varchar(2048) DEFAULT NULL COMMENT '修改前',
    `after_content`     varchar(2048) DEFAULT NULL COMMENT '修改后',
    `operation_content` varchar(2048) DEFAULT NULL COMMENT '修改内容',
    `create_time`       datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户操作日志';

CREATE TABLE `operation_log_11`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `before_content`    varchar(2048) DEFAULT NULL COMMENT '修改前',
    `after_content`     varchar(2048) DEFAULT NULL COMMENT '修改后',
    `operation_content` varchar(2048) DEFAULT NULL COMMENT '修改内容',
    `create_time`       datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户操作日志';

CREATE TABLE `operation_log_12`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `before_content`    varchar(2048) DEFAULT NULL COMMENT '修改前',
    `after_content`     varchar(2048) DEFAULT NULL COMMENT '修改后',
    `operation_content` varchar(2048) DEFAULT NULL COMMENT '修改内容',
    `create_time`       datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户操作日志';

CREATE TABLE `operation_log_13`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `before_content`    varchar(2048) DEFAULT NULL COMMENT '修改前',
    `after_content`     varchar(2048) DEFAULT NULL COMMENT '修改后',
    `operation_content` varchar(2048) DEFAULT NULL COMMENT '修改内容',
    `create_time`       datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户操作日志';

CREATE TABLE `operation_log_14`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `before_content`    varchar(2048) DEFAULT NULL COMMENT '修改前',
    `after_content`     varchar(2048) DEFAULT NULL COMMENT '修改后',
    `operation_content` varchar(2048) DEFAULT NULL COMMENT '修改内容',
    `create_time`       datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户操作日志';

CREATE TABLE `operation_log_15`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `before_content`    varchar(2048) DEFAULT NULL COMMENT '修改前',
    `after_content`     varchar(2048) DEFAULT NULL COMMENT '修改后',
    `operation_content` varchar(2048) DEFAULT NULL COMMENT '修改内容',
    `create_time`       datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户操作日志';

CREATE TABLE `operation_log_2`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `before_content`    varchar(2048) DEFAULT NULL COMMENT '修改前',
    `after_content`     varchar(2048) DEFAULT NULL COMMENT '修改后',
    `operation_content` varchar(2048) DEFAULT NULL COMMENT '修改内容',
    `create_time`       datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户操作日志';

CREATE TABLE `operation_log_3`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `before_content`    varchar(2048) DEFAULT NULL COMMENT '修改前',
    `after_content`     varchar(2048) DEFAULT NULL COMMENT '修改后',
    `operation_content` varchar(2048) DEFAULT NULL COMMENT '修改内容',
    `create_time`       datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户操作日志';

CREATE TABLE `operation_log_4`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `before_content`    varchar(2048) DEFAULT NULL COMMENT '修改前',
    `after_content`     varchar(2048) DEFAULT NULL COMMENT '修改后',
    `operation_content` varchar(2048) DEFAULT NULL COMMENT '修改内容',
    `create_time`       datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户操作日志';

CREATE TABLE `operation_log_5`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `before_content`    varchar(2048) DEFAULT NULL COMMENT '修改前',
    `after_content`     varchar(2048) DEFAULT NULL COMMENT '修改后',
    `operation_content` varchar(2048) DEFAULT NULL COMMENT '修改内容',
    `create_time`       datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户操作日志';

CREATE TABLE `operation_log_6`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `before_content`    varchar(2048) DEFAULT NULL COMMENT '修改前',
    `after_content`     varchar(2048) DEFAULT NULL COMMENT '修改后',
    `operation_content` varchar(2048) DEFAULT NULL COMMENT '修改内容',
    `create_time`       datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户操作日志';

CREATE TABLE `operation_log_7`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `before_content`    varchar(2048) DEFAULT NULL COMMENT '修改前',
    `after_content`     varchar(2048) DEFAULT NULL COMMENT '修改后',
    `operation_content` varchar(2048) DEFAULT NULL COMMENT '修改内容',
    `create_time`       datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户操作日志';

CREATE TABLE `operation_log_8`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `before_content`    varchar(2048) DEFAULT NULL COMMENT '修改前',
    `after_content`     varchar(2048) DEFAULT NULL COMMENT '修改后',
    `operation_content` varchar(2048) DEFAULT NULL COMMENT '修改内容',
    `create_time`       datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户操作日志';

CREATE TABLE `operation_log_9`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `before_content`    varchar(2048) DEFAULT NULL COMMENT '修改前',
    `after_content`     varchar(2048) DEFAULT NULL COMMENT '修改后',
    `operation_content` varchar(2048) DEFAULT NULL COMMENT '修改内容',
    `create_time`       datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户操作日志';

CREATE TABLE `receive_address_0`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `name`             varchar(128)  DEFAULT NULL COMMENT '收货人名称',
    `phone`            varchar(128)  DEFAULT NULL COMMENT '收货人电话',
    `default_flag`     tinyint(1) DEFAULT NULL COMMENT '是否默认 0：否 1：是',
    `tag`              tinyint(1) DEFAULT NULL COMMENT '标签 0：家 1：公司',
    `post_code`        varchar(64)   DEFAULT NULL COMMENT '邮政编码',
    `province`         varchar(64)   DEFAULT NULL COMMENT '省',
    `city`             varchar(64)   DEFAULT NULL COMMENT '市',
    `region`           varchar(64)   DEFAULT NULL COMMENT '区',
    `detail_address`   varchar(2048) DEFAULT NULL COMMENT '详细地址',
    `create_time`      datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1634571960841928705 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户收货地址';

CREATE TABLE `receive_address_1`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `name`             varchar(128) DEFAULT NULL COMMENT '收货人名称',
    `phone`            varchar(128) DEFAULT NULL COMMENT '收货人电话',
    `default_flag`     tinyint(1) DEFAULT NULL COMMENT '是否默认 0：否 1：是',
    `tag`              tinyint(1) DEFAULT NULL COMMENT '标签 0：家 1：公司',
    `post_code`        varchar(64)  DEFAULT NULL COMMENT '邮政编码',
    `province`         varchar(64)  DEFAULT NULL COMMENT '省',
    `city`             varchar(64)  DEFAULT NULL COMMENT '市',
    `region`           varchar(64)  DEFAULT NULL COMMENT '区',
    `detail_address`   varchar(256) DEFAULT NULL COMMENT '详细地址',
    `create_time`      datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户收货地址';

CREATE TABLE `receive_address_10`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `name`             varchar(128) DEFAULT NULL COMMENT '收货人名称',
    `phone`            varchar(128) DEFAULT NULL COMMENT '收货人电话',
    `default_flag`     tinyint(1) DEFAULT NULL COMMENT '是否默认 0：否 1：是',
    `tag`              tinyint(1) DEFAULT NULL COMMENT '标签 0：家 1：公司',
    `post_code`        varchar(64)  DEFAULT NULL COMMENT '邮政编码',
    `province`         varchar(64)  DEFAULT NULL COMMENT '省',
    `city`             varchar(64)  DEFAULT NULL COMMENT '市',
    `region`           varchar(64)  DEFAULT NULL COMMENT '区',
    `detail_address`   varchar(256) DEFAULT NULL COMMENT '详细地址',
    `create_time`      datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户收货地址';

CREATE TABLE `receive_address_11`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `name`             varchar(128) DEFAULT NULL COMMENT '收货人名称',
    `phone`            varchar(128) DEFAULT NULL COMMENT '收货人电话',
    `default_flag`     tinyint(1) DEFAULT NULL COMMENT '是否默认 0：否 1：是',
    `tag`              tinyint(1) DEFAULT NULL COMMENT '标签 0：家 1：公司',
    `post_code`        varchar(64)  DEFAULT NULL COMMENT '邮政编码',
    `province`         varchar(64)  DEFAULT NULL COMMENT '省',
    `city`             varchar(64)  DEFAULT NULL COMMENT '市',
    `region`           varchar(64)  DEFAULT NULL COMMENT '区',
    `detail_address`   varchar(256) DEFAULT NULL COMMENT '详细地址',
    `create_time`      datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户收货地址';

CREATE TABLE `receive_address_12`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `name`             varchar(128) DEFAULT NULL COMMENT '收货人名称',
    `phone`            varchar(128) DEFAULT NULL COMMENT '收货人电话',
    `default_flag`     tinyint(1) DEFAULT NULL COMMENT '是否默认 0：否 1：是',
    `tag`              tinyint(1) DEFAULT NULL COMMENT '标签 0：家 1：公司',
    `post_code`        varchar(64)  DEFAULT NULL COMMENT '邮政编码',
    `province`         varchar(64)  DEFAULT NULL COMMENT '省',
    `city`             varchar(64)  DEFAULT NULL COMMENT '市',
    `region`           varchar(64)  DEFAULT NULL COMMENT '区',
    `detail_address`   varchar(256) DEFAULT NULL COMMENT '详细地址',
    `create_time`      datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户收货地址';

CREATE TABLE `receive_address_13`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `name`             varchar(128) DEFAULT NULL COMMENT '收货人名称',
    `phone`            varchar(128) DEFAULT NULL COMMENT '收货人电话',
    `default_flag`     tinyint(1) DEFAULT NULL COMMENT '是否默认 0：否 1：是',
    `tag`              tinyint(1) DEFAULT NULL COMMENT '标签 0：家 1：公司',
    `post_code`        varchar(64)  DEFAULT NULL COMMENT '邮政编码',
    `province`         varchar(64)  DEFAULT NULL COMMENT '省',
    `city`             varchar(64)  DEFAULT NULL COMMENT '市',
    `region`           varchar(64)  DEFAULT NULL COMMENT '区',
    `detail_address`   varchar(256) DEFAULT NULL COMMENT '详细地址',
    `create_time`      datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户收货地址';

CREATE TABLE `receive_address_14`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `name`             varchar(128) DEFAULT NULL COMMENT '收货人名称',
    `phone`            varchar(128) DEFAULT NULL COMMENT '收货人电话',
    `default_flag`     tinyint(1) DEFAULT NULL COMMENT '是否默认 0：否 1：是',
    `tag`              tinyint(1) DEFAULT NULL COMMENT '标签 0：家 1：公司',
    `post_code`        varchar(64)  DEFAULT NULL COMMENT '邮政编码',
    `province`         varchar(64)  DEFAULT NULL COMMENT '省',
    `city`             varchar(64)  DEFAULT NULL COMMENT '市',
    `region`           varchar(64)  DEFAULT NULL COMMENT '区',
    `detail_address`   varchar(256) DEFAULT NULL COMMENT '详细地址',
    `create_time`      datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户收货地址';

CREATE TABLE `receive_address_15`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `name`             varchar(128) DEFAULT NULL COMMENT '收货人名称',
    `phone`            varchar(128) DEFAULT NULL COMMENT '收货人电话',
    `default_flag`     tinyint(1) DEFAULT NULL COMMENT '是否默认 0：否 1：是',
    `tag`              tinyint(1) DEFAULT NULL COMMENT '标签 0：家 1：公司',
    `post_code`        varchar(64)  DEFAULT NULL COMMENT '邮政编码',
    `province`         varchar(64)  DEFAULT NULL COMMENT '省',
    `city`             varchar(64)  DEFAULT NULL COMMENT '市',
    `region`           varchar(64)  DEFAULT NULL COMMENT '区',
    `detail_address`   varchar(256) DEFAULT NULL COMMENT '详细地址',
    `create_time`      datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户收货地址';

CREATE TABLE `receive_address_2`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `name`             varchar(128) DEFAULT NULL COMMENT '收货人名称',
    `phone`            varchar(128) DEFAULT NULL COMMENT '收货人电话',
    `default_flag`     tinyint(1) DEFAULT NULL COMMENT '是否默认 0：否 1：是',
    `tag`              tinyint(1) DEFAULT NULL COMMENT '标签 0：家 1：公司',
    `post_code`        varchar(64)  DEFAULT NULL COMMENT '邮政编码',
    `province`         varchar(64)  DEFAULT NULL COMMENT '省',
    `city`             varchar(64)  DEFAULT NULL COMMENT '市',
    `region`           varchar(64)  DEFAULT NULL COMMENT '区',
    `detail_address`   varchar(256) DEFAULT NULL COMMENT '详细地址',
    `create_time`      datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户收货地址';

CREATE TABLE `receive_address_3`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `name`             varchar(128) DEFAULT NULL COMMENT '收货人名称',
    `phone`            varchar(128) DEFAULT NULL COMMENT '收货人电话',
    `default_flag`     tinyint(1) DEFAULT NULL COMMENT '是否默认 0：否 1：是',
    `tag`              tinyint(1) DEFAULT NULL COMMENT '标签 0：家 1：公司',
    `post_code`        varchar(64)  DEFAULT NULL COMMENT '邮政编码',
    `province`         varchar(64)  DEFAULT NULL COMMENT '省',
    `city`             varchar(64)  DEFAULT NULL COMMENT '市',
    `region`           varchar(64)  DEFAULT NULL COMMENT '区',
    `detail_address`   varchar(256) DEFAULT NULL COMMENT '详细地址',
    `create_time`      datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户收货地址';

CREATE TABLE `receive_address_4`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `name`             varchar(128) DEFAULT NULL COMMENT '收货人名称',
    `phone`            varchar(128) DEFAULT NULL COMMENT '收货人电话',
    `default_flag`     tinyint(1) DEFAULT NULL COMMENT '是否默认 0：否 1：是',
    `tag`              tinyint(1) DEFAULT NULL COMMENT '标签 0：家 1：公司',
    `post_code`        varchar(64)  DEFAULT NULL COMMENT '邮政编码',
    `province`         varchar(64)  DEFAULT NULL COMMENT '省',
    `city`             varchar(64)  DEFAULT NULL COMMENT '市',
    `region`           varchar(64)  DEFAULT NULL COMMENT '区',
    `detail_address`   varchar(256) DEFAULT NULL COMMENT '详细地址',
    `create_time`      datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户收货地址';

CREATE TABLE `receive_address_5`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `name`             varchar(128) DEFAULT NULL COMMENT '收货人名称',
    `phone`            varchar(128) DEFAULT NULL COMMENT '收货人电话',
    `default_flag`     tinyint(1) DEFAULT NULL COMMENT '是否默认 0：否 1：是',
    `tag`              tinyint(1) DEFAULT NULL COMMENT '标签 0：家 1：公司',
    `post_code`        varchar(64)  DEFAULT NULL COMMENT '邮政编码',
    `province`         varchar(64)  DEFAULT NULL COMMENT '省',
    `city`             varchar(64)  DEFAULT NULL COMMENT '市',
    `region`           varchar(64)  DEFAULT NULL COMMENT '区',
    `detail_address`   varchar(256) DEFAULT NULL COMMENT '详细地址',
    `create_time`      datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户收货地址';

CREATE TABLE `receive_address_6`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `name`             varchar(128) DEFAULT NULL COMMENT '收货人名称',
    `phone`            varchar(128) DEFAULT NULL COMMENT '收货人电话',
    `default_flag`     tinyint(1) DEFAULT NULL COMMENT '是否默认 0：否 1：是',
    `tag`              tinyint(1) DEFAULT NULL COMMENT '标签 0：家 1：公司',
    `post_code`        varchar(64)  DEFAULT NULL COMMENT '邮政编码',
    `province`         varchar(64)  DEFAULT NULL COMMENT '省',
    `city`             varchar(64)  DEFAULT NULL COMMENT '市',
    `region`           varchar(64)  DEFAULT NULL COMMENT '区',
    `detail_address`   varchar(256) DEFAULT NULL COMMENT '详细地址',
    `create_time`      datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户收货地址';

CREATE TABLE `receive_address_7`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `name`             varchar(128) DEFAULT NULL COMMENT '收货人名称',
    `phone`            varchar(128) DEFAULT NULL COMMENT '收货人电话',
    `default_flag`     tinyint(1) DEFAULT NULL COMMENT '是否默认 0：否 1：是',
    `tag`              tinyint(1) DEFAULT NULL COMMENT '标签 0：家 1：公司',
    `post_code`        varchar(64)  DEFAULT NULL COMMENT '邮政编码',
    `province`         varchar(64)  DEFAULT NULL COMMENT '省',
    `city`             varchar(64)  DEFAULT NULL COMMENT '市',
    `region`           varchar(64)  DEFAULT NULL COMMENT '区',
    `detail_address`   varchar(256) DEFAULT NULL COMMENT '详细地址',
    `create_time`      datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户收货地址';

CREATE TABLE `receive_address_8`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `name`             varchar(128) DEFAULT NULL COMMENT '收货人名称',
    `phone`            varchar(128) DEFAULT NULL COMMENT '收货人电话',
    `default_flag`     tinyint(1) DEFAULT NULL COMMENT '是否默认 0：否 1：是',
    `tag`              tinyint(1) DEFAULT NULL COMMENT '标签 0：家 1：公司',
    `post_code`        varchar(64)  DEFAULT NULL COMMENT '邮政编码',
    `province`         varchar(64)  DEFAULT NULL COMMENT '省',
    `city`             varchar(64)  DEFAULT NULL COMMENT '市',
    `region`           varchar(64)  DEFAULT NULL COMMENT '区',
    `detail_address`   varchar(256) DEFAULT NULL COMMENT '详细地址',
    `create_time`      datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户收货地址';

CREATE TABLE `receive_address_9`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT 'C端用户 ID',
    `name`             varchar(128) DEFAULT NULL COMMENT '收货人名称',
    `phone`            varchar(128) DEFAULT NULL COMMENT '收货人电话',
    `default_flag`     tinyint(1) DEFAULT NULL COMMENT '是否默认 0：否 1：是',
    `tag`              tinyint(1) DEFAULT NULL COMMENT '标签 0：家 1：公司',
    `post_code`        varchar(64)  DEFAULT NULL COMMENT '邮政编码',
    `province`         varchar(64)  DEFAULT NULL COMMENT '省',
    `city`             varchar(64)  DEFAULT NULL COMMENT '市',
    `region`           varchar(64)  DEFAULT NULL COMMENT '区',
    `detail_address`   varchar(256) DEFAULT NULL COMMENT '详细地址',
    `create_time`      datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标志',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='C端用户收货地址';

INSERT INTO `customer_user_0` (`id`, `name`, `account_number`, `password`, `phone`, `mail`, `age`, `gender`, `avatar`,
                               `create_time`, `update_time`, `del_flag`)
VALUES ('1634554535496892416', '马称', '*****************', 'sdkfjklKJFpdensfKjnfdiked89ifskj',
        'ur3Hyu1o/ELeCQdFjXsFdA==', '6Wor5LbzhWEm63MNt/OB+MRyV/fnXYa0/rbuw+VdYw8=', NULL, NULL, NULL,
        '2023-03-11 21:59:04', '2023-03-11 21:59:04', '0');

INSERT INTO `receive_address_0` (`id`, `customer_user_id`, `name`, `phone`, `default_flag`, `tag`, `post_code`,
                                 `province`, `city`, `region`, `detail_address`, `create_time`, `update_time`,
                                 `del_flag`)
VALUES ('1634561618543894528', '1634554535496892416', '马邦德', '78spboJThFWneqknNvEGTA==', '0', '0', '100000',
        '北京市', '北京市', '东城区', 'w+KdR1rz+nCaf/V/Tl465gvQ2uEUAK8IiiWEFf7ioCQ=', '2023-03-11 22:27:13',
        '2023-03-11 23:07:38', '0');
