use `congomall_order`;

CREATE TABLE `order_info_0`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`    bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_sn`            varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `total_amount`        decimal(10, 2) DEFAULT NULL COMMENT '订单总金额',
    `pay_amount`          decimal(10, 2) DEFAULT NULL COMMENT '支付金额',
    `freight_amount`      decimal(10, 2) DEFAULT NULL COMMENT '运费金额',
    `pay_type`            int(11) DEFAULT NULL COMMENT '支付方式',
    `pay_time`            datetime       DEFAULT NULL COMMENT '支付时间',
    `source`              tinyint(1) DEFAULT NULL COMMENT '订单来源',
    `type`                tinyint(1) DEFAULT '0' COMMENT '订单类型 0：正常订单 1：秒杀订单 2：促销订单',
    `auto_confirm_day`    int(11) DEFAULT NULL COMMENT '自动确认天数',
    `delivery_company`    varchar(64)    DEFAULT NULL COMMENT '物流公司',
    `delivery_sn`         varchar(64)    DEFAULT NULL COMMENT '物流单号',
    `cnee_name`           varchar(64)    DEFAULT NULL COMMENT '收货人',
    `cnee_phone`          varchar(32)    DEFAULT NULL COMMENT '收货人电话',
    `cnee_post_code`      varchar(32)    DEFAULT NULL COMMENT '收货人邮编',
    `cnee_provinc`        varchar(32)    DEFAULT NULL COMMENT '收货人所在省',
    `cnee_city`           varchar(32)    DEFAULT NULL COMMENT '收货人所在市',
    `cnee_region`         varchar(32)    DEFAULT NULL COMMENT '收货人所在区',
    `cnee_detail_address` varchar(256)   DEFAULT NULL COMMENT '收货人详细地址',
    `receive_time`        datetime       DEFAULT NULL COMMENT '收货时间',
    `remark`              varchar(512)   DEFAULT NULL COMMENT '订单备注信息',
    `confirm_flag`        tinyint(1) DEFAULT '0' COMMENT '收货状态 0：未接收 1：已接收',
    `delivery_time`       datetime       DEFAULT NULL COMMENT '发货时间',
    `status`              tinyint(1) DEFAULT NULL COMMENT '订单状态',
    `create_time`         datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`         datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`            tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1635250563539992577 DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

CREATE TABLE `order_info_1`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`    bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_sn`            varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `total_amount`        decimal(10, 2) DEFAULT NULL COMMENT '订单总金额',
    `pay_amount`          decimal(10, 2) DEFAULT NULL COMMENT '支付金额',
    `freight_amount`      decimal(10, 2) DEFAULT NULL COMMENT '运费金额',
    `pay_type`            int(11) DEFAULT NULL COMMENT '支付方式',
    `pay_time`            datetime       DEFAULT NULL COMMENT '支付时间',
    `source`              tinyint(1) DEFAULT NULL COMMENT '订单来源',
    `type`                tinyint(1) DEFAULT '0' COMMENT '订单类型 0：正常订单 1：秒杀订单 2：促销订单',
    `auto_confirm_day`    int(11) DEFAULT NULL COMMENT '自动确认天数',
    `delivery_company`    varchar(64)    DEFAULT NULL COMMENT '物流公司',
    `delivery_sn`         varchar(64)    DEFAULT NULL COMMENT '物流单号',
    `cnee_name`           varchar(64)    DEFAULT NULL COMMENT '收货人',
    `cnee_phone`          varchar(32)    DEFAULT NULL COMMENT '收货人电话',
    `cnee_post_code`      varchar(32)    DEFAULT NULL COMMENT '收货人邮编',
    `cnee_provinc`        varchar(32)    DEFAULT NULL COMMENT '收货人所在省',
    `cnee_city`           varchar(32)    DEFAULT NULL COMMENT '收货人所在市',
    `cnee_region`         varchar(32)    DEFAULT NULL COMMENT '收货人所在区',
    `cnee_detail_address` varchar(256)   DEFAULT NULL COMMENT '收货人详细地址',
    `receive_time`        datetime       DEFAULT NULL COMMENT '收货时间',
    `remark`              varchar(512)   DEFAULT NULL COMMENT '订单备注信息',
    `confirm_flag`        tinyint(1) DEFAULT '0' COMMENT '收货状态 0：未接收 1：已接收',
    `delivery_time`       datetime       DEFAULT NULL COMMENT '发货时间',
    `status`              tinyint(1) DEFAULT NULL COMMENT '订单状态',
    `create_time`         datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`         datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`            tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1612802327248605185 DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

CREATE TABLE `order_info_10`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`    bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_sn`            varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `total_amount`        decimal(10, 2) DEFAULT NULL COMMENT '订单总金额',
    `pay_amount`          decimal(10, 2) DEFAULT NULL COMMENT '支付金额',
    `freight_amount`      decimal(10, 2) DEFAULT NULL COMMENT '运费金额',
    `pay_type`            int(11) DEFAULT NULL COMMENT '支付方式',
    `pay_time`            datetime       DEFAULT NULL COMMENT '支付时间',
    `source`              tinyint(1) DEFAULT NULL COMMENT '订单来源',
    `type`                tinyint(1) DEFAULT '0' COMMENT '订单类型 0：正常订单 1：秒杀订单 2：促销订单',
    `auto_confirm_day`    int(11) DEFAULT NULL COMMENT '自动确认天数',
    `delivery_company`    varchar(64)    DEFAULT NULL COMMENT '物流公司',
    `delivery_sn`         varchar(64)    DEFAULT NULL COMMENT '物流单号',
    `cnee_name`           varchar(64)    DEFAULT NULL COMMENT '收货人',
    `cnee_phone`          varchar(32)    DEFAULT NULL COMMENT '收货人电话',
    `cnee_post_code`      varchar(32)    DEFAULT NULL COMMENT '收货人邮编',
    `cnee_provinc`        varchar(32)    DEFAULT NULL COMMENT '收货人所在省',
    `cnee_city`           varchar(32)    DEFAULT NULL COMMENT '收货人所在市',
    `cnee_region`         varchar(32)    DEFAULT NULL COMMENT '收货人所在区',
    `cnee_detail_address` varchar(256)   DEFAULT NULL COMMENT '收货人详细地址',
    `receive_time`        datetime       DEFAULT NULL COMMENT '收货时间',
    `remark`              varchar(512)   DEFAULT NULL COMMENT '订单备注信息',
    `confirm_flag`        tinyint(1) DEFAULT '0' COMMENT '收货状态 0：未接收 1：已接收',
    `delivery_time`       datetime       DEFAULT NULL COMMENT '发货时间',
    `status`              tinyint(1) DEFAULT NULL COMMENT '订单状态',
    `create_time`         datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`         datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`            tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1612802327248605185 DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

CREATE TABLE `order_info_11`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`    bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_sn`            varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `total_amount`        decimal(10, 2) DEFAULT NULL COMMENT '订单总金额',
    `pay_amount`          decimal(10, 2) DEFAULT NULL COMMENT '支付金额',
    `freight_amount`      decimal(10, 2) DEFAULT NULL COMMENT '运费金额',
    `pay_type`            int(11) DEFAULT NULL COMMENT '支付方式',
    `pay_time`            datetime       DEFAULT NULL COMMENT '支付时间',
    `source`              tinyint(1) DEFAULT NULL COMMENT '订单来源',
    `type`                tinyint(1) DEFAULT '0' COMMENT '订单类型 0：正常订单 1：秒杀订单 2：促销订单',
    `auto_confirm_day`    int(11) DEFAULT NULL COMMENT '自动确认天数',
    `delivery_company`    varchar(64)    DEFAULT NULL COMMENT '物流公司',
    `delivery_sn`         varchar(64)    DEFAULT NULL COMMENT '物流单号',
    `cnee_name`           varchar(64)    DEFAULT NULL COMMENT '收货人',
    `cnee_phone`          varchar(32)    DEFAULT NULL COMMENT '收货人电话',
    `cnee_post_code`      varchar(32)    DEFAULT NULL COMMENT '收货人邮编',
    `cnee_provinc`        varchar(32)    DEFAULT NULL COMMENT '收货人所在省',
    `cnee_city`           varchar(32)    DEFAULT NULL COMMENT '收货人所在市',
    `cnee_region`         varchar(32)    DEFAULT NULL COMMENT '收货人所在区',
    `cnee_detail_address` varchar(256)   DEFAULT NULL COMMENT '收货人详细地址',
    `receive_time`        datetime       DEFAULT NULL COMMENT '收货时间',
    `remark`              varchar(512)   DEFAULT NULL COMMENT '订单备注信息',
    `confirm_flag`        tinyint(1) DEFAULT '0' COMMENT '收货状态 0：未接收 1：已接收',
    `delivery_time`       datetime       DEFAULT NULL COMMENT '发货时间',
    `status`              tinyint(1) DEFAULT NULL COMMENT '订单状态',
    `create_time`         datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`         datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`            tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1612802327248605185 DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

CREATE TABLE `order_info_12`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`    bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_sn`            varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `total_amount`        decimal(10, 2) DEFAULT NULL COMMENT '订单总金额',
    `pay_amount`          decimal(10, 2) DEFAULT NULL COMMENT '支付金额',
    `freight_amount`      decimal(10, 2) DEFAULT NULL COMMENT '运费金额',
    `pay_type`            int(11) DEFAULT NULL COMMENT '支付方式',
    `pay_time`            datetime       DEFAULT NULL COMMENT '支付时间',
    `source`              tinyint(1) DEFAULT NULL COMMENT '订单来源',
    `type`                tinyint(1) DEFAULT '0' COMMENT '订单类型 0：正常订单 1：秒杀订单 2：促销订单',
    `auto_confirm_day`    int(11) DEFAULT NULL COMMENT '自动确认天数',
    `delivery_company`    varchar(64)    DEFAULT NULL COMMENT '物流公司',
    `delivery_sn`         varchar(64)    DEFAULT NULL COMMENT '物流单号',
    `cnee_name`           varchar(64)    DEFAULT NULL COMMENT '收货人',
    `cnee_phone`          varchar(32)    DEFAULT NULL COMMENT '收货人电话',
    `cnee_post_code`      varchar(32)    DEFAULT NULL COMMENT '收货人邮编',
    `cnee_provinc`        varchar(32)    DEFAULT NULL COMMENT '收货人所在省',
    `cnee_city`           varchar(32)    DEFAULT NULL COMMENT '收货人所在市',
    `cnee_region`         varchar(32)    DEFAULT NULL COMMENT '收货人所在区',
    `cnee_detail_address` varchar(256)   DEFAULT NULL COMMENT '收货人详细地址',
    `receive_time`        datetime       DEFAULT NULL COMMENT '收货时间',
    `remark`              varchar(512)   DEFAULT NULL COMMENT '订单备注信息',
    `confirm_flag`        tinyint(1) DEFAULT '0' COMMENT '收货状态 0：未接收 1：已接收',
    `delivery_time`       datetime       DEFAULT NULL COMMENT '发货时间',
    `status`              tinyint(1) DEFAULT NULL COMMENT '订单状态',
    `create_time`         datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`         datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`            tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1612802327248605185 DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

CREATE TABLE `order_info_13`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`    bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_sn`            varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `total_amount`        decimal(10, 2) DEFAULT NULL COMMENT '订单总金额',
    `pay_amount`          decimal(10, 2) DEFAULT NULL COMMENT '支付金额',
    `freight_amount`      decimal(10, 2) DEFAULT NULL COMMENT '运费金额',
    `pay_type`            int(11) DEFAULT NULL COMMENT '支付方式',
    `pay_time`            datetime       DEFAULT NULL COMMENT '支付时间',
    `source`              tinyint(1) DEFAULT NULL COMMENT '订单来源',
    `type`                tinyint(1) DEFAULT '0' COMMENT '订单类型 0：正常订单 1：秒杀订单 2：促销订单',
    `auto_confirm_day`    int(11) DEFAULT NULL COMMENT '自动确认天数',
    `delivery_company`    varchar(64)    DEFAULT NULL COMMENT '物流公司',
    `delivery_sn`         varchar(64)    DEFAULT NULL COMMENT '物流单号',
    `cnee_name`           varchar(64)    DEFAULT NULL COMMENT '收货人',
    `cnee_phone`          varchar(32)    DEFAULT NULL COMMENT '收货人电话',
    `cnee_post_code`      varchar(32)    DEFAULT NULL COMMENT '收货人邮编',
    `cnee_provinc`        varchar(32)    DEFAULT NULL COMMENT '收货人所在省',
    `cnee_city`           varchar(32)    DEFAULT NULL COMMENT '收货人所在市',
    `cnee_region`         varchar(32)    DEFAULT NULL COMMENT '收货人所在区',
    `cnee_detail_address` varchar(256)   DEFAULT NULL COMMENT '收货人详细地址',
    `receive_time`        datetime       DEFAULT NULL COMMENT '收货时间',
    `remark`              varchar(512)   DEFAULT NULL COMMENT '订单备注信息',
    `confirm_flag`        tinyint(1) DEFAULT '0' COMMENT '收货状态 0：未接收 1：已接收',
    `delivery_time`       datetime       DEFAULT NULL COMMENT '发货时间',
    `status`              tinyint(1) DEFAULT NULL COMMENT '订单状态',
    `create_time`         datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`         datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`            tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1612802327248605185 DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

CREATE TABLE `order_info_14`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`    bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_sn`            varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `total_amount`        decimal(10, 2) DEFAULT NULL COMMENT '订单总金额',
    `pay_amount`          decimal(10, 2) DEFAULT NULL COMMENT '支付金额',
    `freight_amount`      decimal(10, 2) DEFAULT NULL COMMENT '运费金额',
    `pay_type`            int(11) DEFAULT NULL COMMENT '支付方式',
    `pay_time`            datetime       DEFAULT NULL COMMENT '支付时间',
    `source`              tinyint(1) DEFAULT NULL COMMENT '订单来源',
    `type`                tinyint(1) DEFAULT '0' COMMENT '订单类型 0：正常订单 1：秒杀订单 2：促销订单',
    `auto_confirm_day`    int(11) DEFAULT NULL COMMENT '自动确认天数',
    `delivery_company`    varchar(64)    DEFAULT NULL COMMENT '物流公司',
    `delivery_sn`         varchar(64)    DEFAULT NULL COMMENT '物流单号',
    `cnee_name`           varchar(64)    DEFAULT NULL COMMENT '收货人',
    `cnee_phone`          varchar(32)    DEFAULT NULL COMMENT '收货人电话',
    `cnee_post_code`      varchar(32)    DEFAULT NULL COMMENT '收货人邮编',
    `cnee_provinc`        varchar(32)    DEFAULT NULL COMMENT '收货人所在省',
    `cnee_city`           varchar(32)    DEFAULT NULL COMMENT '收货人所在市',
    `cnee_region`         varchar(32)    DEFAULT NULL COMMENT '收货人所在区',
    `cnee_detail_address` varchar(256)   DEFAULT NULL COMMENT '收货人详细地址',
    `receive_time`        datetime       DEFAULT NULL COMMENT '收货时间',
    `remark`              varchar(512)   DEFAULT NULL COMMENT '订单备注信息',
    `confirm_flag`        tinyint(1) DEFAULT '0' COMMENT '收货状态 0：未接收 1：已接收',
    `delivery_time`       datetime       DEFAULT NULL COMMENT '发货时间',
    `status`              tinyint(1) DEFAULT NULL COMMENT '订单状态',
    `create_time`         datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`         datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`            tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1612802327248605185 DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

CREATE TABLE `order_info_15`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`    bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_sn`            varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `total_amount`        decimal(10, 2) DEFAULT NULL COMMENT '订单总金额',
    `pay_amount`          decimal(10, 2) DEFAULT NULL COMMENT '支付金额',
    `freight_amount`      decimal(10, 2) DEFAULT NULL COMMENT '运费金额',
    `pay_type`            int(11) DEFAULT NULL COMMENT '支付方式',
    `pay_time`            datetime       DEFAULT NULL COMMENT '支付时间',
    `source`              tinyint(1) DEFAULT NULL COMMENT '订单来源',
    `type`                tinyint(1) DEFAULT '0' COMMENT '订单类型 0：正常订单 1：秒杀订单 2：促销订单',
    `auto_confirm_day`    int(11) DEFAULT NULL COMMENT '自动确认天数',
    `delivery_company`    varchar(64)    DEFAULT NULL COMMENT '物流公司',
    `delivery_sn`         varchar(64)    DEFAULT NULL COMMENT '物流单号',
    `cnee_name`           varchar(64)    DEFAULT NULL COMMENT '收货人',
    `cnee_phone`          varchar(32)    DEFAULT NULL COMMENT '收货人电话',
    `cnee_post_code`      varchar(32)    DEFAULT NULL COMMENT '收货人邮编',
    `cnee_provinc`        varchar(32)    DEFAULT NULL COMMENT '收货人所在省',
    `cnee_city`           varchar(32)    DEFAULT NULL COMMENT '收货人所在市',
    `cnee_region`         varchar(32)    DEFAULT NULL COMMENT '收货人所在区',
    `cnee_detail_address` varchar(256)   DEFAULT NULL COMMENT '收货人详细地址',
    `receive_time`        datetime       DEFAULT NULL COMMENT '收货时间',
    `remark`              varchar(512)   DEFAULT NULL COMMENT '订单备注信息',
    `confirm_flag`        tinyint(1) DEFAULT '0' COMMENT '收货状态 0：未接收 1：已接收',
    `delivery_time`       datetime       DEFAULT NULL COMMENT '发货时间',
    `status`              tinyint(1) DEFAULT NULL COMMENT '订单状态',
    `create_time`         datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`         datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`            tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1612802327248605185 DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

CREATE TABLE `order_info_2`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`    bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_sn`            varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `total_amount`        decimal(10, 2) DEFAULT NULL COMMENT '订单总金额',
    `pay_amount`          decimal(10, 2) DEFAULT NULL COMMENT '支付金额',
    `freight_amount`      decimal(10, 2) DEFAULT NULL COMMENT '运费金额',
    `pay_type`            int(11) DEFAULT NULL COMMENT '支付方式',
    `pay_time`            datetime       DEFAULT NULL COMMENT '支付时间',
    `source`              tinyint(1) DEFAULT NULL COMMENT '订单来源',
    `type`                tinyint(1) DEFAULT '0' COMMENT '订单类型 0：正常订单 1：秒杀订单 2：促销订单',
    `auto_confirm_day`    int(11) DEFAULT NULL COMMENT '自动确认天数',
    `delivery_company`    varchar(64)    DEFAULT NULL COMMENT '物流公司',
    `delivery_sn`         varchar(64)    DEFAULT NULL COMMENT '物流单号',
    `cnee_name`           varchar(64)    DEFAULT NULL COMMENT '收货人',
    `cnee_phone`          varchar(32)    DEFAULT NULL COMMENT '收货人电话',
    `cnee_post_code`      varchar(32)    DEFAULT NULL COMMENT '收货人邮编',
    `cnee_provinc`        varchar(32)    DEFAULT NULL COMMENT '收货人所在省',
    `cnee_city`           varchar(32)    DEFAULT NULL COMMENT '收货人所在市',
    `cnee_region`         varchar(32)    DEFAULT NULL COMMENT '收货人所在区',
    `cnee_detail_address` varchar(256)   DEFAULT NULL COMMENT '收货人详细地址',
    `receive_time`        datetime       DEFAULT NULL COMMENT '收货时间',
    `remark`              varchar(512)   DEFAULT NULL COMMENT '订单备注信息',
    `confirm_flag`        tinyint(1) DEFAULT '0' COMMENT '收货状态 0：未接收 1：已接收',
    `delivery_time`       datetime       DEFAULT NULL COMMENT '发货时间',
    `status`              tinyint(1) DEFAULT NULL COMMENT '订单状态',
    `create_time`         datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`         datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`            tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1612802327248605185 DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

CREATE TABLE `order_info_3`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`    bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_sn`            varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `total_amount`        decimal(10, 2) DEFAULT NULL COMMENT '订单总金额',
    `pay_amount`          decimal(10, 2) DEFAULT NULL COMMENT '支付金额',
    `freight_amount`      decimal(10, 2) DEFAULT NULL COMMENT '运费金额',
    `pay_type`            int(11) DEFAULT NULL COMMENT '支付方式',
    `pay_time`            datetime       DEFAULT NULL COMMENT '支付时间',
    `source`              tinyint(1) DEFAULT NULL COMMENT '订单来源',
    `type`                tinyint(1) DEFAULT '0' COMMENT '订单类型 0：正常订单 1：秒杀订单 2：促销订单',
    `auto_confirm_day`    int(11) DEFAULT NULL COMMENT '自动确认天数',
    `delivery_company`    varchar(64)    DEFAULT NULL COMMENT '物流公司',
    `delivery_sn`         varchar(64)    DEFAULT NULL COMMENT '物流单号',
    `cnee_name`           varchar(64)    DEFAULT NULL COMMENT '收货人',
    `cnee_phone`          varchar(32)    DEFAULT NULL COMMENT '收货人电话',
    `cnee_post_code`      varchar(32)    DEFAULT NULL COMMENT '收货人邮编',
    `cnee_provinc`        varchar(32)    DEFAULT NULL COMMENT '收货人所在省',
    `cnee_city`           varchar(32)    DEFAULT NULL COMMENT '收货人所在市',
    `cnee_region`         varchar(32)    DEFAULT NULL COMMENT '收货人所在区',
    `cnee_detail_address` varchar(256)   DEFAULT NULL COMMENT '收货人详细地址',
    `receive_time`        datetime       DEFAULT NULL COMMENT '收货时间',
    `remark`              varchar(512)   DEFAULT NULL COMMENT '订单备注信息',
    `confirm_flag`        tinyint(1) DEFAULT '0' COMMENT '收货状态 0：未接收 1：已接收',
    `delivery_time`       datetime       DEFAULT NULL COMMENT '发货时间',
    `status`              tinyint(1) DEFAULT NULL COMMENT '订单状态',
    `create_time`         datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`         datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`            tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1612802327248605185 DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

CREATE TABLE `order_info_4`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`    bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_sn`            varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `total_amount`        decimal(10, 2) DEFAULT NULL COMMENT '订单总金额',
    `pay_amount`          decimal(10, 2) DEFAULT NULL COMMENT '支付金额',
    `freight_amount`      decimal(10, 2) DEFAULT NULL COMMENT '运费金额',
    `pay_type`            int(11) DEFAULT NULL COMMENT '支付方式',
    `pay_time`            datetime       DEFAULT NULL COMMENT '支付时间',
    `source`              tinyint(1) DEFAULT NULL COMMENT '订单来源',
    `type`                tinyint(1) DEFAULT '0' COMMENT '订单类型 0：正常订单 1：秒杀订单 2：促销订单',
    `auto_confirm_day`    int(11) DEFAULT NULL COMMENT '自动确认天数',
    `delivery_company`    varchar(64)    DEFAULT NULL COMMENT '物流公司',
    `delivery_sn`         varchar(64)    DEFAULT NULL COMMENT '物流单号',
    `cnee_name`           varchar(64)    DEFAULT NULL COMMENT '收货人',
    `cnee_phone`          varchar(32)    DEFAULT NULL COMMENT '收货人电话',
    `cnee_post_code`      varchar(32)    DEFAULT NULL COMMENT '收货人邮编',
    `cnee_provinc`        varchar(32)    DEFAULT NULL COMMENT '收货人所在省',
    `cnee_city`           varchar(32)    DEFAULT NULL COMMENT '收货人所在市',
    `cnee_region`         varchar(32)    DEFAULT NULL COMMENT '收货人所在区',
    `cnee_detail_address` varchar(256)   DEFAULT NULL COMMENT '收货人详细地址',
    `receive_time`        datetime       DEFAULT NULL COMMENT '收货时间',
    `remark`              varchar(512)   DEFAULT NULL COMMENT '订单备注信息',
    `confirm_flag`        tinyint(1) DEFAULT '0' COMMENT '收货状态 0：未接收 1：已接收',
    `delivery_time`       datetime       DEFAULT NULL COMMENT '发货时间',
    `status`              tinyint(1) DEFAULT NULL COMMENT '订单状态',
    `create_time`         datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`         datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`            tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1614954038913142785 DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

CREATE TABLE `order_info_5`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`    bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_sn`            varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `total_amount`        decimal(10, 2) DEFAULT NULL COMMENT '订单总金额',
    `pay_amount`          decimal(10, 2) DEFAULT NULL COMMENT '支付金额',
    `freight_amount`      decimal(10, 2) DEFAULT NULL COMMENT '运费金额',
    `pay_type`            int(11) DEFAULT NULL COMMENT '支付方式',
    `pay_time`            datetime       DEFAULT NULL COMMENT '支付时间',
    `source`              tinyint(1) DEFAULT NULL COMMENT '订单来源',
    `type`                tinyint(1) DEFAULT '0' COMMENT '订单类型 0：正常订单 1：秒杀订单 2：促销订单',
    `auto_confirm_day`    int(11) DEFAULT NULL COMMENT '自动确认天数',
    `delivery_company`    varchar(64)    DEFAULT NULL COMMENT '物流公司',
    `delivery_sn`         varchar(64)    DEFAULT NULL COMMENT '物流单号',
    `cnee_name`           varchar(64)    DEFAULT NULL COMMENT '收货人',
    `cnee_phone`          varchar(32)    DEFAULT NULL COMMENT '收货人电话',
    `cnee_post_code`      varchar(32)    DEFAULT NULL COMMENT '收货人邮编',
    `cnee_provinc`        varchar(32)    DEFAULT NULL COMMENT '收货人所在省',
    `cnee_city`           varchar(32)    DEFAULT NULL COMMENT '收货人所在市',
    `cnee_region`         varchar(32)    DEFAULT NULL COMMENT '收货人所在区',
    `cnee_detail_address` varchar(256)   DEFAULT NULL COMMENT '收货人详细地址',
    `receive_time`        datetime       DEFAULT NULL COMMENT '收货时间',
    `remark`              varchar(512)   DEFAULT NULL COMMENT '订单备注信息',
    `confirm_flag`        tinyint(1) DEFAULT '0' COMMENT '收货状态 0：未接收 1：已接收',
    `delivery_time`       datetime       DEFAULT NULL COMMENT '发货时间',
    `status`              tinyint(1) DEFAULT NULL COMMENT '订单状态',
    `create_time`         datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`         datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`            tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1612802327248605185 DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

CREATE TABLE `order_info_6`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`    bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_sn`            varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `total_amount`        decimal(10, 2) DEFAULT NULL COMMENT '订单总金额',
    `pay_amount`          decimal(10, 2) DEFAULT NULL COMMENT '支付金额',
    `freight_amount`      decimal(10, 2) DEFAULT NULL COMMENT '运费金额',
    `pay_type`            int(11) DEFAULT NULL COMMENT '支付方式',
    `pay_time`            datetime       DEFAULT NULL COMMENT '支付时间',
    `source`              tinyint(1) DEFAULT NULL COMMENT '订单来源',
    `type`                tinyint(1) DEFAULT '0' COMMENT '订单类型 0：正常订单 1：秒杀订单 2：促销订单',
    `auto_confirm_day`    int(11) DEFAULT NULL COMMENT '自动确认天数',
    `delivery_company`    varchar(64)    DEFAULT NULL COMMENT '物流公司',
    `delivery_sn`         varchar(64)    DEFAULT NULL COMMENT '物流单号',
    `cnee_name`           varchar(64)    DEFAULT NULL COMMENT '收货人',
    `cnee_phone`          varchar(32)    DEFAULT NULL COMMENT '收货人电话',
    `cnee_post_code`      varchar(32)    DEFAULT NULL COMMENT '收货人邮编',
    `cnee_provinc`        varchar(32)    DEFAULT NULL COMMENT '收货人所在省',
    `cnee_city`           varchar(32)    DEFAULT NULL COMMENT '收货人所在市',
    `cnee_region`         varchar(32)    DEFAULT NULL COMMENT '收货人所在区',
    `cnee_detail_address` varchar(256)   DEFAULT NULL COMMENT '收货人详细地址',
    `receive_time`        datetime       DEFAULT NULL COMMENT '收货时间',
    `remark`              varchar(512)   DEFAULT NULL COMMENT '订单备注信息',
    `confirm_flag`        tinyint(1) DEFAULT '0' COMMENT '收货状态 0：未接收 1：已接收',
    `delivery_time`       datetime       DEFAULT NULL COMMENT '发货时间',
    `status`              tinyint(1) DEFAULT NULL COMMENT '订单状态',
    `create_time`         datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`         datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`            tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1612802327248605185 DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

CREATE TABLE `order_info_7`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`    bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_sn`            varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `total_amount`        decimal(10, 2) DEFAULT NULL COMMENT '订单总金额',
    `pay_amount`          decimal(10, 2) DEFAULT NULL COMMENT '支付金额',
    `freight_amount`      decimal(10, 2) DEFAULT NULL COMMENT '运费金额',
    `pay_type`            int(11) DEFAULT NULL COMMENT '支付方式',
    `pay_time`            datetime       DEFAULT NULL COMMENT '支付时间',
    `source`              tinyint(1) DEFAULT NULL COMMENT '订单来源',
    `type`                tinyint(1) DEFAULT '0' COMMENT '订单类型 0：正常订单 1：秒杀订单 2：促销订单',
    `auto_confirm_day`    int(11) DEFAULT NULL COMMENT '自动确认天数',
    `delivery_company`    varchar(64)    DEFAULT NULL COMMENT '物流公司',
    `delivery_sn`         varchar(64)    DEFAULT NULL COMMENT '物流单号',
    `cnee_name`           varchar(64)    DEFAULT NULL COMMENT '收货人',
    `cnee_phone`          varchar(32)    DEFAULT NULL COMMENT '收货人电话',
    `cnee_post_code`      varchar(32)    DEFAULT NULL COMMENT '收货人邮编',
    `cnee_provinc`        varchar(32)    DEFAULT NULL COMMENT '收货人所在省',
    `cnee_city`           varchar(32)    DEFAULT NULL COMMENT '收货人所在市',
    `cnee_region`         varchar(32)    DEFAULT NULL COMMENT '收货人所在区',
    `cnee_detail_address` varchar(256)   DEFAULT NULL COMMENT '收货人详细地址',
    `receive_time`        datetime       DEFAULT NULL COMMENT '收货时间',
    `remark`              varchar(512)   DEFAULT NULL COMMENT '订单备注信息',
    `confirm_flag`        tinyint(1) DEFAULT '0' COMMENT '收货状态 0：未接收 1：已接收',
    `delivery_time`       datetime       DEFAULT NULL COMMENT '发货时间',
    `status`              tinyint(1) DEFAULT NULL COMMENT '订单状态',
    `create_time`         datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`         datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`            tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1612802327248605185 DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

CREATE TABLE `order_info_8`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`    bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_sn`            varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `total_amount`        decimal(10, 2) DEFAULT NULL COMMENT '订单总金额',
    `pay_amount`          decimal(10, 2) DEFAULT NULL COMMENT '支付金额',
    `freight_amount`      decimal(10, 2) DEFAULT NULL COMMENT '运费金额',
    `pay_type`            int(11) DEFAULT NULL COMMENT '支付方式',
    `pay_time`            datetime       DEFAULT NULL COMMENT '支付时间',
    `source`              tinyint(1) DEFAULT NULL COMMENT '订单来源',
    `type`                tinyint(1) DEFAULT '0' COMMENT '订单类型 0：正常订单 1：秒杀订单 2：促销订单',
    `auto_confirm_day`    int(11) DEFAULT NULL COMMENT '自动确认天数',
    `delivery_company`    varchar(64)    DEFAULT NULL COMMENT '物流公司',
    `delivery_sn`         varchar(64)    DEFAULT NULL COMMENT '物流单号',
    `cnee_name`           varchar(64)    DEFAULT NULL COMMENT '收货人',
    `cnee_phone`          varchar(32)    DEFAULT NULL COMMENT '收货人电话',
    `cnee_post_code`      varchar(32)    DEFAULT NULL COMMENT '收货人邮编',
    `cnee_provinc`        varchar(32)    DEFAULT NULL COMMENT '收货人所在省',
    `cnee_city`           varchar(32)    DEFAULT NULL COMMENT '收货人所在市',
    `cnee_region`         varchar(32)    DEFAULT NULL COMMENT '收货人所在区',
    `cnee_detail_address` varchar(256)   DEFAULT NULL COMMENT '收货人详细地址',
    `receive_time`        datetime       DEFAULT NULL COMMENT '收货时间',
    `remark`              varchar(512)   DEFAULT NULL COMMENT '订单备注信息',
    `confirm_flag`        tinyint(1) DEFAULT '0' COMMENT '收货状态 0：未接收 1：已接收',
    `delivery_time`       datetime       DEFAULT NULL COMMENT '发货时间',
    `status`              tinyint(1) DEFAULT NULL COMMENT '订单状态',
    `create_time`         datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`         datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`            tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1612802327248605185 DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

CREATE TABLE `order_info_9`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`    bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_sn`            varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `total_amount`        decimal(10, 2) DEFAULT NULL COMMENT '订单总金额',
    `pay_amount`          decimal(10, 2) DEFAULT NULL COMMENT '支付金额',
    `freight_amount`      decimal(10, 2) DEFAULT NULL COMMENT '运费金额',
    `pay_type`            int(11) DEFAULT NULL COMMENT '支付方式',
    `pay_time`            datetime       DEFAULT NULL COMMENT '支付时间',
    `source`              tinyint(1) DEFAULT NULL COMMENT '订单来源',
    `type`                tinyint(1) DEFAULT '0' COMMENT '订单类型 0：正常订单 1：秒杀订单 2：促销订单',
    `auto_confirm_day`    int(11) DEFAULT NULL COMMENT '自动确认天数',
    `delivery_company`    varchar(64)    DEFAULT NULL COMMENT '物流公司',
    `delivery_sn`         varchar(64)    DEFAULT NULL COMMENT '物流单号',
    `cnee_name`           varchar(64)    DEFAULT NULL COMMENT '收货人',
    `cnee_phone`          varchar(32)    DEFAULT NULL COMMENT '收货人电话',
    `cnee_post_code`      varchar(32)    DEFAULT NULL COMMENT '收货人邮编',
    `cnee_provinc`        varchar(32)    DEFAULT NULL COMMENT '收货人所在省',
    `cnee_city`           varchar(32)    DEFAULT NULL COMMENT '收货人所在市',
    `cnee_region`         varchar(32)    DEFAULT NULL COMMENT '收货人所在区',
    `cnee_detail_address` varchar(256)   DEFAULT NULL COMMENT '收货人详细地址',
    `receive_time`        datetime       DEFAULT NULL COMMENT '收货时间',
    `remark`              varchar(512)   DEFAULT NULL COMMENT '订单备注信息',
    `confirm_flag`        tinyint(1) DEFAULT '0' COMMENT '收货状态 0：未接收 1：已接收',
    `delivery_time`       datetime       DEFAULT NULL COMMENT '发货时间',
    `status`              tinyint(1) DEFAULT NULL COMMENT '订单状态',
    `create_time`         datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`         datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`            tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1612802327248605185 DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

CREATE TABLE `order_item_0`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_id`          bigint(20) DEFAULT NULL COMMENT '订单ID',
    `order_sn`          varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `product_id`        bigint(20) DEFAULT NULL COMMENT '商品SPU ID',
    `product_sku_id`    bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `product_pic`       varchar(256)   DEFAULT NULL COMMENT '商品图',
    `product_name`      varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_brand`     varchar(256)   DEFAULT NULL COMMENT '商品品牌',
    `product_price`     decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `product_quantity`  int(11) DEFAULT NULL COMMENT '商品购买数量',
    `product_attribute` varchar(1024)  DEFAULT NULL COMMENT '规格，JSON 格式',
    `create_time`       datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1635250575363735553 DEFAULT CHARSET=utf8mb4 COMMENT='订单明细表';

CREATE TABLE `order_item_1`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_id`          bigint(20) DEFAULT NULL COMMENT '订单ID',
    `order_sn`          varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `product_id`        bigint(20) DEFAULT NULL COMMENT '商品SPU ID',
    `product_sku_id`    bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `product_pic`       varchar(256)   DEFAULT NULL COMMENT '商品图',
    `product_name`      varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_brand`     varchar(256)   DEFAULT NULL COMMENT '商品品牌',
    `product_price`     decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `product_quantity`  int(11) DEFAULT NULL COMMENT '商品购买数量',
    `product_attribute` varchar(1024)  DEFAULT NULL COMMENT '规格，JSON 格式',
    `create_time`       datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1612802332663451649 DEFAULT CHARSET=utf8mb4 COMMENT='订单明细表';

CREATE TABLE `order_item_10`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_id`          bigint(20) DEFAULT NULL COMMENT '订单ID',
    `order_sn`          varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `product_id`        bigint(20) DEFAULT NULL COMMENT '商品SPU ID',
    `product_sku_id`    bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `product_pic`       varchar(256)   DEFAULT NULL COMMENT '商品图',
    `product_name`      varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_brand`     varchar(256)   DEFAULT NULL COMMENT '商品品牌',
    `product_price`     decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `product_quantity`  int(11) DEFAULT NULL COMMENT '商品购买数量',
    `product_attribute` varchar(1024)  DEFAULT NULL COMMENT '规格，JSON 格式',
    `create_time`       datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1612802332663451649 DEFAULT CHARSET=utf8mb4 COMMENT='订单明细表';

CREATE TABLE `order_item_11`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_id`          bigint(20) DEFAULT NULL COMMENT '订单ID',
    `order_sn`          varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `product_id`        bigint(20) DEFAULT NULL COMMENT '商品SPU ID',
    `product_sku_id`    bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `product_pic`       varchar(256)   DEFAULT NULL COMMENT '商品图',
    `product_name`      varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_brand`     varchar(256)   DEFAULT NULL COMMENT '商品品牌',
    `product_price`     decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `product_quantity`  int(11) DEFAULT NULL COMMENT '商品购买数量',
    `product_attribute` varchar(1024)  DEFAULT NULL COMMENT '规格，JSON 格式',
    `create_time`       datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1612802332663451649 DEFAULT CHARSET=utf8mb4 COMMENT='订单明细表';

CREATE TABLE `order_item_12`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_id`          bigint(20) DEFAULT NULL COMMENT '订单ID',
    `order_sn`          varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `product_id`        bigint(20) DEFAULT NULL COMMENT '商品SPU ID',
    `product_sku_id`    bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `product_pic`       varchar(256)   DEFAULT NULL COMMENT '商品图',
    `product_name`      varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_brand`     varchar(256)   DEFAULT NULL COMMENT '商品品牌',
    `product_price`     decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `product_quantity`  int(11) DEFAULT NULL COMMENT '商品购买数量',
    `product_attribute` varchar(1024)  DEFAULT NULL COMMENT '规格，JSON 格式',
    `create_time`       datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1612802332663451649 DEFAULT CHARSET=utf8mb4 COMMENT='订单明细表';

CREATE TABLE `order_item_13`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_id`          bigint(20) DEFAULT NULL COMMENT '订单ID',
    `order_sn`          varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `product_id`        bigint(20) DEFAULT NULL COMMENT '商品SPU ID',
    `product_sku_id`    bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `product_pic`       varchar(256)   DEFAULT NULL COMMENT '商品图',
    `product_name`      varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_brand`     varchar(256)   DEFAULT NULL COMMENT '商品品牌',
    `product_price`     decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `product_quantity`  int(11) DEFAULT NULL COMMENT '商品购买数量',
    `product_attribute` varchar(1024)  DEFAULT NULL COMMENT '规格，JSON 格式',
    `create_time`       datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1612802332663451649 DEFAULT CHARSET=utf8mb4 COMMENT='订单明细表';

CREATE TABLE `order_item_14`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_id`          bigint(20) DEFAULT NULL COMMENT '订单ID',
    `order_sn`          varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `product_id`        bigint(20) DEFAULT NULL COMMENT '商品SPU ID',
    `product_sku_id`    bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `product_pic`       varchar(256)   DEFAULT NULL COMMENT '商品图',
    `product_name`      varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_brand`     varchar(256)   DEFAULT NULL COMMENT '商品品牌',
    `product_price`     decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `product_quantity`  int(11) DEFAULT NULL COMMENT '商品购买数量',
    `product_attribute` varchar(1024)  DEFAULT NULL COMMENT '规格，JSON 格式',
    `create_time`       datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1612802332663451649 DEFAULT CHARSET=utf8mb4 COMMENT='订单明细表';

CREATE TABLE `order_item_15`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_id`          bigint(20) DEFAULT NULL COMMENT '订单ID',
    `order_sn`          varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `product_id`        bigint(20) DEFAULT NULL COMMENT '商品SPU ID',
    `product_sku_id`    bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `product_pic`       varchar(256)   DEFAULT NULL COMMENT '商品图',
    `product_name`      varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_brand`     varchar(256)   DEFAULT NULL COMMENT '商品品牌',
    `product_price`     decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `product_quantity`  int(11) DEFAULT NULL COMMENT '商品购买数量',
    `product_attribute` varchar(1024)  DEFAULT NULL COMMENT '规格，JSON 格式',
    `create_time`       datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1612802332663451649 DEFAULT CHARSET=utf8mb4 COMMENT='订单明细表';

CREATE TABLE `order_item_2`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_id`          bigint(20) DEFAULT NULL COMMENT '订单ID',
    `order_sn`          varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `product_id`        bigint(20) DEFAULT NULL COMMENT '商品SPU ID',
    `product_sku_id`    bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `product_pic`       varchar(256)   DEFAULT NULL COMMENT '商品图',
    `product_name`      varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_brand`     varchar(256)   DEFAULT NULL COMMENT '商品品牌',
    `product_price`     decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `product_quantity`  int(11) DEFAULT NULL COMMENT '商品购买数量',
    `product_attribute` varchar(1024)  DEFAULT NULL COMMENT '规格，JSON 格式',
    `create_time`       datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1612802332663451649 DEFAULT CHARSET=utf8mb4 COMMENT='订单明细表';

CREATE TABLE `order_item_3`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_id`          bigint(20) DEFAULT NULL COMMENT '订单ID',
    `order_sn`          varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `product_id`        bigint(20) DEFAULT NULL COMMENT '商品SPU ID',
    `product_sku_id`    bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `product_pic`       varchar(256)   DEFAULT NULL COMMENT '商品图',
    `product_name`      varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_brand`     varchar(256)   DEFAULT NULL COMMENT '商品品牌',
    `product_price`     decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `product_quantity`  int(11) DEFAULT NULL COMMENT '商品购买数量',
    `product_attribute` varchar(1024)  DEFAULT NULL COMMENT '规格，JSON 格式',
    `create_time`       datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1612802332663451649 DEFAULT CHARSET=utf8mb4 COMMENT='订单明细表';

CREATE TABLE `order_item_4`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_id`          bigint(20) DEFAULT NULL COMMENT '订单ID',
    `order_sn`          varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `product_id`        bigint(20) DEFAULT NULL COMMENT '商品SPU ID',
    `product_sku_id`    bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `product_pic`       varchar(256)   DEFAULT NULL COMMENT '商品图',
    `product_name`      varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_brand`     varchar(256)   DEFAULT NULL COMMENT '商品品牌',
    `product_price`     decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `product_quantity`  int(11) DEFAULT NULL COMMENT '商品购买数量',
    `product_attribute` varchar(1024)  DEFAULT NULL COMMENT '规格，JSON 格式',
    `create_time`       datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1612802332663451649 DEFAULT CHARSET=utf8mb4 COMMENT='订单明细表';

CREATE TABLE `order_item_5`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_id`          bigint(20) DEFAULT NULL COMMENT '订单ID',
    `order_sn`          varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `product_id`        bigint(20) DEFAULT NULL COMMENT '商品SPU ID',
    `product_sku_id`    bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `product_pic`       varchar(256)   DEFAULT NULL COMMENT '商品图',
    `product_name`      varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_brand`     varchar(256)   DEFAULT NULL COMMENT '商品品牌',
    `product_price`     decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `product_quantity`  int(11) DEFAULT NULL COMMENT '商品购买数量',
    `product_attribute` varchar(1024)  DEFAULT NULL COMMENT '规格，JSON 格式',
    `create_time`       datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1612802332663451649 DEFAULT CHARSET=utf8mb4 COMMENT='订单明细表';

CREATE TABLE `order_item_6`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_id`          bigint(20) DEFAULT NULL COMMENT '订单ID',
    `order_sn`          varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `product_id`        bigint(20) DEFAULT NULL COMMENT '商品SPU ID',
    `product_sku_id`    bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `product_pic`       varchar(256)   DEFAULT NULL COMMENT '商品图',
    `product_name`      varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_brand`     varchar(256)   DEFAULT NULL COMMENT '商品品牌',
    `product_price`     decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `product_quantity`  int(11) DEFAULT NULL COMMENT '商品购买数量',
    `product_attribute` varchar(1024)  DEFAULT NULL COMMENT '规格，JSON 格式',
    `create_time`       datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1612802332663451649 DEFAULT CHARSET=utf8mb4 COMMENT='订单明细表';

CREATE TABLE `order_item_7`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_id`          bigint(20) DEFAULT NULL COMMENT '订单ID',
    `order_sn`          varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `product_id`        bigint(20) DEFAULT NULL COMMENT '商品SPU ID',
    `product_sku_id`    bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `product_pic`       varchar(256)   DEFAULT NULL COMMENT '商品图',
    `product_name`      varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_brand`     varchar(256)   DEFAULT NULL COMMENT '商品品牌',
    `product_price`     decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `product_quantity`  int(11) DEFAULT NULL COMMENT '商品购买数量',
    `product_attribute` varchar(1024)  DEFAULT NULL COMMENT '规格，JSON 格式',
    `create_time`       datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1612802332663451649 DEFAULT CHARSET=utf8mb4 COMMENT='订单明细表';

CREATE TABLE `order_item_8`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_id`          bigint(20) DEFAULT NULL COMMENT '订单ID',
    `order_sn`          varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `product_id`        bigint(20) DEFAULT NULL COMMENT '商品SPU ID',
    `product_sku_id`    bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `product_pic`       varchar(256)   DEFAULT NULL COMMENT '商品图',
    `product_name`      varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_brand`     varchar(256)   DEFAULT NULL COMMENT '商品品牌',
    `product_price`     decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `product_quantity`  int(11) DEFAULT NULL COMMENT '商品购买数量',
    `product_attribute` varchar(1024)  DEFAULT NULL COMMENT '规格，JSON 格式',
    `create_time`       datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1612802332663451649 DEFAULT CHARSET=utf8mb4 COMMENT='订单明细表';

CREATE TABLE `order_item_9`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `customer_user_id`  bigint(20) DEFAULT NULL COMMENT '用户ID',
    `order_id`          bigint(20) DEFAULT NULL COMMENT '订单ID',
    `order_sn`          varchar(64)    DEFAULT NULL COMMENT '订单编号',
    `product_id`        bigint(20) DEFAULT NULL COMMENT '商品SPU ID',
    `product_sku_id`    bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `product_pic`       varchar(256)   DEFAULT NULL COMMENT '商品图',
    `product_name`      varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_brand`     varchar(256)   DEFAULT NULL COMMENT '商品品牌',
    `product_price`     decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `product_quantity`  int(11) DEFAULT NULL COMMENT '商品购买数量',
    `product_attribute` varchar(1024)  DEFAULT NULL COMMENT '规格，JSON 格式',
    `create_time`       datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`          tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1612802332663451649 DEFAULT CHARSET=utf8mb4 COMMENT='订单明细表';

CREATE TABLE `undo_log`
(
    `branch_id`     bigint(20) NOT NULL COMMENT 'branch transaction id',
    `xid`           varchar(128) NOT NULL COMMENT 'global transaction id',
    `context`       varchar(128) NOT NULL COMMENT 'undo_log context,such as serialization',
    `rollback_info` longblob     NOT NULL COMMENT 'rollback info',
    `log_status`    int(11) NOT NULL COMMENT '0:normal status,1:defense status',
    `log_created`   datetime(6) NOT NULL COMMENT 'create datetime',
    `log_modified`  datetime(6) NOT NULL COMMENT 'modify datetime',
    UNIQUE KEY `ux_undo_log` (`xid`,`branch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AT transaction mode undo table';
