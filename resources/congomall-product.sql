use congomall_product;

CREATE TABLE `product_attribute`
(
    `id`                            bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `product_attribute_category_id` bigint(20) DEFAULT NULL COMMENT '商品属性分类 ID',
    `name`                          varchar(128)  DEFAULT NULL COMMENT '名称',
    `option_status`                 tinyint(1) DEFAULT NULL COMMENT '可选状态 0：手动录入 1：代入可选集合',
    `option_list`                   varchar(1024) DEFAULT NULL COMMENT '可选值集合',
    `sort`                          int(11) DEFAULT NULL COMMENT '排序',
    `type`                          tinyint(1) DEFAULT NULL COMMENT '类型 0：规则 1：参数',
    `create_time`                   datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`                   datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`                      tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1477056202448494593 DEFAULT CHARSET=utf8mb4 COMMENT='商品属性表';

CREATE TABLE `product_attribute_category`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name`        varchar(128) DEFAULT NULL COMMENT '分类名称',
    `create_time` datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`    tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1477056215689912321 DEFAULT CHARSET=utf8mb4 COMMENT='商品属性分类表';

CREATE TABLE `product_attribute_value`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `product_id`           bigint(20) DEFAULT NULL COMMENT '商品 ID',
    `product_attribute_id` bigint(20) DEFAULT NULL COMMENT '商品属性 ID',
    `attribute_value`      varchar(1024) DEFAULT NULL COMMENT '商品属性值',
    `create_time`          datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`          datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`             tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1477056215706689537 DEFAULT CHARSET=utf8mb4 COMMENT='商品属性值表';

CREATE TABLE `product_brand`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name`        varchar(256) DEFAULT NULL COMMENT '品牌名称',
    `desc`        varchar(512) DEFAULT NULL COMMENT '品牌介绍',
    `pic`         varchar(256) DEFAULT NULL COMMENT '品牌图',
    `sort`        int(11) DEFAULT NULL COMMENT '排序',
    `create_time` datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`    tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1477055818267025409 DEFAULT CHARSET=utf8mb4 COMMENT='商品品牌表';

CREATE TABLE `product_category`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name`        varchar(64)  DEFAULT NULL COMMENT '分类名称',
    `parent_id`   bigint(20) DEFAULT NULL COMMENT '父级 ID',
    `level`       tinyint(1) DEFAULT NULL COMMENT '层级',
    `icon_url`    varchar(256) DEFAULT NULL COMMENT '图标 URL',
    `sort`        int(11) DEFAULT NULL COMMENT '排序',
    `url`         varchar(256) DEFAULT NULL COMMENT '跳转地址',
    `status`      tinyint(1) DEFAULT NULL COMMENT '状态 0：展示 1：隐藏',
    `create_time` datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime     DEFAULT NULL COMMENT '修改时间',
    `del_flag`    tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1477055850177290241 DEFAULT CHARSET=utf8mb4 COMMENT='商品分类表';

CREATE TABLE `product_comment_0`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `parent_id`        bigint(20) DEFAULT '0' COMMENT '上级ID，一级评论为0',
    `product_id`       bigint(20) DEFAULT NULL COMMENT '商品ID',
    `product_sku_id`   bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
    `like_count`       int(3) DEFAULT '0' COMMENT '点赞数',
    `reply_count`      int(3) DEFAULT '0' COMMENT '回复数',
    `star`             int(11) DEFAULT NULL COMMENT '评分',
    `content`          varchar(2048) DEFAULT NULL COMMENT '评论',
    `comment_flag`     tinyint(1) DEFAULT NULL COMMENT '回复标识 0：用户 1：店家',
    `hide_flag`        tinyint(1) DEFAULT NULL COMMENT '匿名标识 0：匿名 1：不匿名',
    `append_flag`      tinyint(1) DEFAULT '0' COMMENT '追加标识 0：否 1：是',
    `resource`         varchar(2048) DEFAULT NULL COMMENT '评论图片/视频，JSON格式',
    `create_time`      datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                `idx_parent_id` (`parent_id`) USING BTREE,
    KEY                `idx_product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477055850177290241 DEFAULT CHARSET=utf8mb4 COMMENT='商品评论表';

CREATE TABLE `product_comment_1`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `parent_id`        bigint(20) DEFAULT '0' COMMENT '上级ID，一级评论为0',
    `product_id`       bigint(20) DEFAULT NULL COMMENT '商品ID',
    `product_sku_id`   bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
    `like_count`       int(3) DEFAULT '0' COMMENT '点赞数',
    `reply_count`      int(3) DEFAULT '0' COMMENT '回复数',
    `star`             int(11) DEFAULT NULL COMMENT '评分',
    `content`          varchar(2048) DEFAULT NULL COMMENT '评论',
    `comment_flag`     tinyint(1) DEFAULT NULL COMMENT '回复标识 0：用户 1：店家',
    `hide_flag`        tinyint(1) DEFAULT NULL COMMENT '匿名标识 0：匿名 1：不匿名',
    `append_flag`      tinyint(1) DEFAULT '0' COMMENT '追加标识 0：否 1：是',
    `resource`         varchar(2048) DEFAULT NULL COMMENT '评论图片/视频，JSON格式',
    `create_time`      datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                `idx_parent_id` (`parent_id`) USING BTREE,
    KEY                `idx_product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477055850177290241 DEFAULT CHARSET=utf8mb4 COMMENT='商品评论表';

CREATE TABLE `product_comment_10`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `parent_id`        bigint(20) DEFAULT '0' COMMENT '上级ID，一级评论为0',
    `product_id`       bigint(20) DEFAULT NULL COMMENT '商品ID',
    `product_sku_id`   bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
    `like_count`       int(3) DEFAULT '0' COMMENT '点赞数',
    `reply_count`      int(3) DEFAULT '0' COMMENT '回复数',
    `star`             int(11) DEFAULT NULL COMMENT '评分',
    `content`          varchar(2048) DEFAULT NULL COMMENT '评论',
    `comment_flag`     tinyint(1) DEFAULT NULL COMMENT '回复标识 0：用户 1：店家',
    `hide_flag`        tinyint(1) DEFAULT NULL COMMENT '匿名标识 0：匿名 1：不匿名',
    `append_flag`      tinyint(1) DEFAULT '0' COMMENT '追加标识 0：否 1：是',
    `resource`         varchar(2048) DEFAULT NULL COMMENT '评论图片/视频，JSON格式',
    `create_time`      datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                `idx_parent_id` (`parent_id`) USING BTREE,
    KEY                `idx_product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477055850177290241 DEFAULT CHARSET=utf8mb4 COMMENT='商品评论表';

CREATE TABLE `product_comment_11`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `parent_id`        bigint(20) DEFAULT '0' COMMENT '上级ID，一级评论为0',
    `product_id`       bigint(20) DEFAULT NULL COMMENT '商品ID',
    `product_sku_id`   bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
    `like_count`       int(3) DEFAULT '0' COMMENT '点赞数',
    `reply_count`      int(3) DEFAULT '0' COMMENT '回复数',
    `star`             int(11) DEFAULT NULL COMMENT '评分',
    `content`          varchar(2048) DEFAULT NULL COMMENT '评论',
    `comment_flag`     tinyint(1) DEFAULT NULL COMMENT '回复标识 0：用户 1：店家',
    `hide_flag`        tinyint(1) DEFAULT NULL COMMENT '匿名标识 0：匿名 1：不匿名',
    `append_flag`      tinyint(1) DEFAULT '0' COMMENT '追加标识 0：否 1：是',
    `resource`         varchar(2048) DEFAULT NULL COMMENT '评论图片/视频，JSON格式',
    `create_time`      datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                `idx_parent_id` (`parent_id`) USING BTREE,
    KEY                `idx_product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477055850177290241 DEFAULT CHARSET=utf8mb4 COMMENT='商品评论表';

CREATE TABLE `product_comment_12`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `parent_id`        bigint(20) DEFAULT '0' COMMENT '上级ID，一级评论为0',
    `product_id`       bigint(20) DEFAULT NULL COMMENT '商品ID',
    `product_sku_id`   bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
    `like_count`       int(3) DEFAULT '0' COMMENT '点赞数',
    `reply_count`      int(3) DEFAULT '0' COMMENT '回复数',
    `star`             int(11) DEFAULT NULL COMMENT '评分',
    `content`          varchar(2048) DEFAULT NULL COMMENT '评论',
    `comment_flag`     tinyint(1) DEFAULT NULL COMMENT '回复标识 0：用户 1：店家',
    `hide_flag`        tinyint(1) DEFAULT NULL COMMENT '匿名标识 0：匿名 1：不匿名',
    `append_flag`      tinyint(1) DEFAULT '0' COMMENT '追加标识 0：否 1：是',
    `resource`         varchar(2048) DEFAULT NULL COMMENT '评论图片/视频，JSON格式',
    `create_time`      datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                `idx_parent_id` (`parent_id`) USING BTREE,
    KEY                `idx_product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477055850177290241 DEFAULT CHARSET=utf8mb4 COMMENT='商品评论表';

CREATE TABLE `product_comment_13`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `parent_id`        bigint(20) DEFAULT '0' COMMENT '上级ID，一级评论为0',
    `product_id`       bigint(20) DEFAULT NULL COMMENT '商品ID',
    `product_sku_id`   bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
    `like_count`       int(3) DEFAULT '0' COMMENT '点赞数',
    `reply_count`      int(3) DEFAULT '0' COMMENT '回复数',
    `star`             int(11) DEFAULT NULL COMMENT '评分',
    `content`          varchar(2048) DEFAULT NULL COMMENT '评论',
    `comment_flag`     tinyint(1) DEFAULT NULL COMMENT '回复标识 0：用户 1：店家',
    `hide_flag`        tinyint(1) DEFAULT NULL COMMENT '匿名标识 0：匿名 1：不匿名',
    `append_flag`      tinyint(1) DEFAULT '0' COMMENT '追加标识 0：否 1：是',
    `resource`         varchar(2048) DEFAULT NULL COMMENT '评论图片/视频，JSON格式',
    `create_time`      datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                `idx_parent_id` (`parent_id`) USING BTREE,
    KEY                `idx_product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477055850177290241 DEFAULT CHARSET=utf8mb4 COMMENT='商品评论表';

CREATE TABLE `product_comment_14`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `parent_id`        bigint(20) DEFAULT '0' COMMENT '上级ID，一级评论为0',
    `product_id`       bigint(20) DEFAULT NULL COMMENT '商品ID',
    `product_sku_id`   bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
    `like_count`       int(3) DEFAULT '0' COMMENT '点赞数',
    `reply_count`      int(3) DEFAULT '0' COMMENT '回复数',
    `star`             int(11) DEFAULT NULL COMMENT '评分',
    `content`          varchar(2048) DEFAULT NULL COMMENT '评论',
    `comment_flag`     tinyint(1) DEFAULT NULL COMMENT '回复标识 0：用户 1：店家',
    `hide_flag`        tinyint(1) DEFAULT NULL COMMENT '匿名标识 0：匿名 1：不匿名',
    `append_flag`      tinyint(1) DEFAULT '0' COMMENT '追加标识 0：否 1：是',
    `resource`         varchar(2048) DEFAULT NULL COMMENT '评论图片/视频，JSON格式',
    `create_time`      datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                `idx_parent_id` (`parent_id`) USING BTREE,
    KEY                `idx_product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477055850177290241 DEFAULT CHARSET=utf8mb4 COMMENT='商品评论表';

CREATE TABLE `product_comment_15`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `parent_id`        bigint(20) DEFAULT '0' COMMENT '上级ID，一级评论为0',
    `product_id`       bigint(20) DEFAULT NULL COMMENT '商品ID',
    `product_sku_id`   bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
    `like_count`       int(3) DEFAULT '0' COMMENT '点赞数',
    `reply_count`      int(3) DEFAULT '0' COMMENT '回复数',
    `star`             int(11) DEFAULT NULL COMMENT '评分',
    `content`          varchar(2048) DEFAULT NULL COMMENT '评论',
    `comment_flag`     tinyint(1) DEFAULT NULL COMMENT '回复标识 0：用户 1：店家',
    `hide_flag`        tinyint(1) DEFAULT NULL COMMENT '匿名标识 0：匿名 1：不匿名',
    `append_flag`      tinyint(1) DEFAULT '0' COMMENT '追加标识 0：否 1：是',
    `resource`         varchar(2048) DEFAULT NULL COMMENT '评论图片/视频，JSON格式',
    `create_time`      datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                `idx_parent_id` (`parent_id`) USING BTREE,
    KEY                `idx_product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477055850177290241 DEFAULT CHARSET=utf8mb4 COMMENT='商品评论表';

CREATE TABLE `product_comment_2`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `parent_id`        bigint(20) DEFAULT '0' COMMENT '上级ID，一级评论为0',
    `product_id`       bigint(20) DEFAULT NULL COMMENT '商品ID',
    `product_sku_id`   bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
    `like_count`       int(3) DEFAULT '0' COMMENT '点赞数',
    `reply_count`      int(3) DEFAULT '0' COMMENT '回复数',
    `star`             int(11) DEFAULT NULL COMMENT '评分',
    `content`          varchar(2048) DEFAULT NULL COMMENT '评论',
    `comment_flag`     tinyint(1) DEFAULT NULL COMMENT '回复标识 0：用户 1：店家',
    `hide_flag`        tinyint(1) DEFAULT NULL COMMENT '匿名标识 0：匿名 1：不匿名',
    `append_flag`      tinyint(1) DEFAULT '0' COMMENT '追加标识 0：否 1：是',
    `resource`         varchar(2048) DEFAULT NULL COMMENT '评论图片/视频，JSON格式',
    `create_time`      datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                `idx_parent_id` (`parent_id`) USING BTREE,
    KEY                `idx_product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477055850177290241 DEFAULT CHARSET=utf8mb4 COMMENT='商品评论表';

CREATE TABLE `product_comment_3`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `parent_id`        bigint(20) DEFAULT '0' COMMENT '上级ID，一级评论为0',
    `product_id`       bigint(20) DEFAULT NULL COMMENT '商品ID',
    `product_sku_id`   bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
    `like_count`       int(3) DEFAULT '0' COMMENT '点赞数',
    `reply_count`      int(3) DEFAULT '0' COMMENT '回复数',
    `star`             int(11) DEFAULT NULL COMMENT '评分',
    `content`          varchar(2048) DEFAULT NULL COMMENT '评论',
    `comment_flag`     tinyint(1) DEFAULT NULL COMMENT '回复标识 0：用户 1：店家',
    `hide_flag`        tinyint(1) DEFAULT NULL COMMENT '匿名标识 0：匿名 1：不匿名',
    `append_flag`      tinyint(1) DEFAULT '0' COMMENT '追加标识 0：否 1：是',
    `resource`         varchar(2048) DEFAULT NULL COMMENT '评论图片/视频，JSON格式',
    `create_time`      datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                `idx_parent_id` (`parent_id`) USING BTREE,
    KEY                `idx_product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477055850177290241 DEFAULT CHARSET=utf8mb4 COMMENT='商品评论表';

CREATE TABLE `product_comment_4`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `parent_id`        bigint(20) DEFAULT '0' COMMENT '上级ID，一级评论为0',
    `product_id`       bigint(20) DEFAULT NULL COMMENT '商品ID',
    `product_sku_id`   bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
    `like_count`       int(3) DEFAULT '0' COMMENT '点赞数',
    `reply_count`      int(3) DEFAULT '0' COMMENT '回复数',
    `star`             int(11) DEFAULT NULL COMMENT '评分',
    `content`          varchar(2048) DEFAULT NULL COMMENT '评论',
    `comment_flag`     tinyint(1) DEFAULT NULL COMMENT '回复标识 0：用户 1：店家',
    `hide_flag`        tinyint(1) DEFAULT NULL COMMENT '匿名标识 0：匿名 1：不匿名',
    `append_flag`      tinyint(1) DEFAULT '0' COMMENT '追加标识 0：否 1：是',
    `resource`         varchar(2048) DEFAULT NULL COMMENT '评论图片/视频，JSON格式',
    `create_time`      datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                `idx_parent_id` (`parent_id`) USING BTREE,
    KEY                `idx_product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477055850177290241 DEFAULT CHARSET=utf8mb4 COMMENT='商品评论表';

CREATE TABLE `product_comment_5`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `parent_id`        bigint(20) DEFAULT '0' COMMENT '上级ID，一级评论为0',
    `product_id`       bigint(20) DEFAULT NULL COMMENT '商品ID',
    `product_sku_id`   bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
    `like_count`       int(3) DEFAULT '0' COMMENT '点赞数',
    `reply_count`      int(3) DEFAULT '0' COMMENT '回复数',
    `star`             int(11) DEFAULT NULL COMMENT '评分',
    `content`          varchar(2048) DEFAULT NULL COMMENT '评论',
    `comment_flag`     tinyint(1) DEFAULT NULL COMMENT '回复标识 0：用户 1：店家',
    `hide_flag`        tinyint(1) DEFAULT NULL COMMENT '匿名标识 0：匿名 1：不匿名',
    `append_flag`      tinyint(1) DEFAULT '0' COMMENT '追加标识 0：否 1：是',
    `resource`         varchar(2048) DEFAULT NULL COMMENT '评论图片/视频，JSON格式',
    `create_time`      datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                `idx_parent_id` (`parent_id`) USING BTREE,
    KEY                `idx_product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477055850177290241 DEFAULT CHARSET=utf8mb4 COMMENT='商品评论表';

CREATE TABLE `product_comment_6`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `parent_id`        bigint(20) DEFAULT '0' COMMENT '上级ID，一级评论为0',
    `product_id`       bigint(20) DEFAULT NULL COMMENT '商品ID',
    `product_sku_id`   bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
    `like_count`       int(3) DEFAULT '0' COMMENT '点赞数',
    `reply_count`      int(3) DEFAULT '0' COMMENT '回复数',
    `star`             int(11) DEFAULT NULL COMMENT '评分',
    `content`          varchar(2048) DEFAULT NULL COMMENT '评论',
    `comment_flag`     tinyint(1) DEFAULT NULL COMMENT '回复标识 0：用户 1：店家',
    `hide_flag`        tinyint(1) DEFAULT NULL COMMENT '匿名标识 0：匿名 1：不匿名',
    `append_flag`      tinyint(1) DEFAULT '0' COMMENT '追加标识 0：否 1：是',
    `resource`         varchar(2048) DEFAULT NULL COMMENT '评论图片/视频，JSON格式',
    `create_time`      datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                `idx_parent_id` (`parent_id`) USING BTREE,
    KEY                `idx_product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477055850177290241 DEFAULT CHARSET=utf8mb4 COMMENT='商品评论表';

CREATE TABLE `product_comment_7`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `parent_id`        bigint(20) DEFAULT '0' COMMENT '上级ID，一级评论为0',
    `product_id`       bigint(20) DEFAULT NULL COMMENT '商品ID',
    `product_sku_id`   bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
    `like_count`       int(3) DEFAULT '0' COMMENT '点赞数',
    `reply_count`      int(3) DEFAULT '0' COMMENT '回复数',
    `star`             int(11) DEFAULT NULL COMMENT '评分',
    `content`          varchar(2048) DEFAULT NULL COMMENT '评论',
    `comment_flag`     tinyint(1) DEFAULT NULL COMMENT '回复标识 0：用户 1：店家',
    `hide_flag`        tinyint(1) DEFAULT NULL COMMENT '匿名标识 0：匿名 1：不匿名',
    `append_flag`      tinyint(1) DEFAULT '0' COMMENT '追加标识 0：否 1：是',
    `resource`         varchar(2048) DEFAULT NULL COMMENT '评论图片/视频，JSON格式',
    `create_time`      datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                `idx_parent_id` (`parent_id`) USING BTREE,
    KEY                `idx_product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1634916431458807809 DEFAULT CHARSET=utf8mb4 COMMENT='商品评论表';

CREATE TABLE `product_comment_8`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `parent_id`        bigint(20) DEFAULT '0' COMMENT '上级ID，一级评论为0',
    `product_id`       bigint(20) DEFAULT NULL COMMENT '商品ID',
    `product_sku_id`   bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
    `like_count`       int(3) DEFAULT '0' COMMENT '点赞数',
    `reply_count`      int(3) DEFAULT '0' COMMENT '回复数',
    `star`             int(11) DEFAULT NULL COMMENT '评分',
    `content`          varchar(2048) DEFAULT NULL COMMENT '评论',
    `comment_flag`     tinyint(1) DEFAULT NULL COMMENT '回复标识 0：用户 1：店家',
    `hide_flag`        tinyint(1) DEFAULT NULL COMMENT '匿名标识 0：匿名 1：不匿名',
    `append_flag`      tinyint(1) DEFAULT '0' COMMENT '追加标识 0：否 1：是',
    `resource`         varchar(2048) DEFAULT NULL COMMENT '评论图片/视频，JSON格式',
    `create_time`      datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                `idx_parent_id` (`parent_id`) USING BTREE,
    KEY                `idx_product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477055850177290241 DEFAULT CHARSET=utf8mb4 COMMENT='商品评论表';

CREATE TABLE `product_comment_9`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `parent_id`        bigint(20) DEFAULT '0' COMMENT '上级ID，一级评论为0',
    `product_id`       bigint(20) DEFAULT NULL COMMENT '商品ID',
    `product_sku_id`   bigint(20) DEFAULT NULL COMMENT '商品SKU ID',
    `customer_user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
    `like_count`       int(3) DEFAULT '0' COMMENT '点赞数',
    `reply_count`      int(3) DEFAULT '0' COMMENT '回复数',
    `star`             int(11) DEFAULT NULL COMMENT '评分',
    `content`          varchar(2048) DEFAULT NULL COMMENT '评论',
    `comment_flag`     tinyint(1) DEFAULT NULL COMMENT '回复标识 0：用户 1：店家',
    `hide_flag`        tinyint(1) DEFAULT NULL COMMENT '匿名标识 0：匿名 1：不匿名',
    `append_flag`      tinyint(1) DEFAULT '0' COMMENT '追加标识 0：否 1：是',
    `resource`         varchar(2048) DEFAULT NULL COMMENT '评论图片/视频，JSON格式',
    `create_time`      datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime      DEFAULT NULL COMMENT '修改时间',
    `del_flag`         tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                `idx_parent_id` (`parent_id`) USING BTREE,
    KEY                `idx_product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477055850177290241 DEFAULT CHARSET=utf8mb4 COMMENT='商品评论表';

CREATE TABLE `product_sku_0`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id` bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`    bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `product_id`  bigint(20) DEFAULT NULL COMMENT '商品ID',
    `price`       decimal(10, 2) DEFAULT NULL COMMENT '价格',
    `stock`       int(11) DEFAULT '0' COMMENT '库存',
    `lock_stock`  int(11) DEFAULT '0' COMMENT '锁定库存',
    `pic`         varchar(256)   DEFAULT NULL COMMENT '图片',
    `attribute`   varchar(1024)  DEFAULT NULL COMMENT '属性，JSON 格式',
    `create_time` datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`    tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY           `idx_brand_id_product_id` (`brand_id`,`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477056338335555585 DEFAULT CHARSET=utf8mb4 COMMENT='商品库存表';

CREATE TABLE `product_sku_1`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id` bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`    bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `product_id`  bigint(20) DEFAULT NULL COMMENT '商品ID',
    `price`       decimal(10, 2) DEFAULT NULL COMMENT '价格',
    `stock`       int(11) DEFAULT '0' COMMENT '库存',
    `lock_stock`  int(11) DEFAULT '0' COMMENT '锁定库存',
    `pic`         varchar(256)   DEFAULT NULL COMMENT '图片',
    `attribute`   varchar(1024)  DEFAULT NULL COMMENT '属性，JSON 格式',
    `create_time` datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`    tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY           `idx_brand_id_product_id` (`brand_id`,`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477056338335555585 DEFAULT CHARSET=utf8mb4 COMMENT='商品库存表';

CREATE TABLE `product_sku_10`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id` bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`    bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `product_id`  bigint(20) DEFAULT NULL COMMENT '商品ID',
    `price`       decimal(10, 2) DEFAULT NULL COMMENT '价格',
    `stock`       int(11) DEFAULT '0' COMMENT '库存',
    `lock_stock`  int(11) DEFAULT '0' COMMENT '锁定库存',
    `pic`         varchar(256)   DEFAULT NULL COMMENT '图片',
    `attribute`   varchar(1024)  DEFAULT NULL COMMENT '属性，JSON 格式',
    `create_time` datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`    tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY           `idx_brand_id_product_id` (`brand_id`,`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477056338335555585 DEFAULT CHARSET=utf8mb4 COMMENT='商品库存表';

CREATE TABLE `product_sku_11`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id` bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`    bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `product_id`  bigint(20) DEFAULT NULL COMMENT '商品ID',
    `price`       decimal(10, 2) DEFAULT NULL COMMENT '价格',
    `stock`       int(11) DEFAULT '0' COMMENT '库存',
    `lock_stock`  int(11) DEFAULT '0' COMMENT '锁定库存',
    `pic`         varchar(256)   DEFAULT NULL COMMENT '图片',
    `attribute`   varchar(1024)  DEFAULT NULL COMMENT '属性，JSON 格式',
    `create_time` datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`    tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY           `idx_brand_id_product_id` (`brand_id`,`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477056338335555585 DEFAULT CHARSET=utf8mb4 COMMENT='商品库存表';

CREATE TABLE `product_sku_12`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id` bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`    bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `product_id`  bigint(20) DEFAULT NULL COMMENT '商品ID',
    `price`       decimal(10, 2) DEFAULT NULL COMMENT '价格',
    `stock`       int(11) DEFAULT '0' COMMENT '库存',
    `lock_stock`  int(11) DEFAULT '0' COMMENT '锁定库存',
    `pic`         varchar(256)   DEFAULT NULL COMMENT '图片',
    `attribute`   varchar(1024)  DEFAULT NULL COMMENT '属性，JSON 格式',
    `create_time` datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`    tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY           `idx_brand_id_product_id` (`brand_id`,`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477056338335555585 DEFAULT CHARSET=utf8mb4 COMMENT='商品库存表';

CREATE TABLE `product_sku_13`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id` bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`    bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `product_id`  bigint(20) DEFAULT NULL COMMENT '商品ID',
    `price`       decimal(10, 2) DEFAULT NULL COMMENT '价格',
    `stock`       int(11) DEFAULT '0' COMMENT '库存',
    `lock_stock`  int(11) DEFAULT '0' COMMENT '锁定库存',
    `pic`         varchar(256)   DEFAULT NULL COMMENT '图片',
    `attribute`   varchar(1024)  DEFAULT NULL COMMENT '属性，JSON 格式',
    `create_time` datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`    tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY           `idx_brand_id_product_id` (`brand_id`,`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477056338335555585 DEFAULT CHARSET=utf8mb4 COMMENT='商品库存表';

CREATE TABLE `product_sku_14`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id` bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`    bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `product_id`  bigint(20) DEFAULT NULL COMMENT '商品ID',
    `price`       decimal(10, 2) DEFAULT NULL COMMENT '价格',
    `stock`       int(11) DEFAULT '0' COMMENT '库存',
    `lock_stock`  int(11) DEFAULT '0' COMMENT '锁定库存',
    `pic`         varchar(256)   DEFAULT NULL COMMENT '图片',
    `attribute`   varchar(1024)  DEFAULT NULL COMMENT '属性，JSON 格式',
    `create_time` datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`    tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY           `idx_brand_id_product_id` (`brand_id`,`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477056338335555585 DEFAULT CHARSET=utf8mb4 COMMENT='商品库存表';

CREATE TABLE `product_sku_15`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id` bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`    bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `product_id`  bigint(20) DEFAULT NULL COMMENT '商品ID',
    `price`       decimal(10, 2) DEFAULT NULL COMMENT '价格',
    `stock`       int(11) DEFAULT '0' COMMENT '库存',
    `lock_stock`  int(11) DEFAULT '0' COMMENT '锁定库存',
    `pic`         varchar(256)   DEFAULT NULL COMMENT '图片',
    `attribute`   varchar(1024)  DEFAULT NULL COMMENT '属性，JSON 格式',
    `create_time` datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`    tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY           `idx_brand_id_product_id` (`brand_id`,`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477056338335555585 DEFAULT CHARSET=utf8mb4 COMMENT='商品库存表';

CREATE TABLE `product_sku_2`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id` bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`    bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `product_id`  bigint(20) DEFAULT NULL COMMENT '商品ID',
    `price`       decimal(10, 2) DEFAULT NULL COMMENT '价格',
    `stock`       int(11) DEFAULT '0' COMMENT '库存',
    `lock_stock`  int(11) DEFAULT '0' COMMENT '锁定库存',
    `pic`         varchar(256)   DEFAULT NULL COMMENT '图片',
    `attribute`   varchar(1024)  DEFAULT NULL COMMENT '属性，JSON 格式',
    `create_time` datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`    tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY           `idx_brand_id_product_id` (`brand_id`,`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477056338335555585 DEFAULT CHARSET=utf8mb4 COMMENT='商品库存表';

CREATE TABLE `product_sku_3`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id` bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`    bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `product_id`  bigint(20) DEFAULT NULL COMMENT '商品ID',
    `price`       decimal(10, 2) DEFAULT NULL COMMENT '价格',
    `stock`       int(11) DEFAULT '0' COMMENT '库存',
    `lock_stock`  int(11) DEFAULT '0' COMMENT '锁定库存',
    `pic`         varchar(256)   DEFAULT NULL COMMENT '图片',
    `attribute`   varchar(1024)  DEFAULT NULL COMMENT '属性，JSON 格式',
    `create_time` datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`    tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY           `idx_brand_id_product_id` (`brand_id`,`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477056338335555585 DEFAULT CHARSET=utf8mb4 COMMENT='商品库存表';

CREATE TABLE `product_sku_4`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id` bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`    bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `product_id`  bigint(20) DEFAULT NULL COMMENT '商品ID',
    `price`       decimal(10, 2) DEFAULT NULL COMMENT '价格',
    `stock`       int(11) DEFAULT '0' COMMENT '库存',
    `lock_stock`  int(11) DEFAULT '0' COMMENT '锁定库存',
    `pic`         varchar(256)   DEFAULT NULL COMMENT '图片',
    `attribute`   varchar(1024)  DEFAULT NULL COMMENT '属性，JSON 格式',
    `create_time` datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`    tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY           `idx_brand_id_product_id` (`brand_id`,`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477056338335555585 DEFAULT CHARSET=utf8mb4 COMMENT='商品库存表';

CREATE TABLE `product_sku_5`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id` bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`    bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `product_id`  bigint(20) DEFAULT NULL COMMENT '商品ID',
    `price`       decimal(10, 2) DEFAULT NULL COMMENT '价格',
    `stock`       int(11) DEFAULT '0' COMMENT '库存',
    `lock_stock`  int(11) DEFAULT '0' COMMENT '锁定库存',
    `pic`         varchar(256)   DEFAULT NULL COMMENT '图片',
    `attribute`   varchar(1024)  DEFAULT NULL COMMENT '属性，JSON 格式',
    `create_time` datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`    tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY           `idx_brand_id_product_id` (`brand_id`,`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477056338335555585 DEFAULT CHARSET=utf8mb4 COMMENT='商品库存表';

CREATE TABLE `product_sku_6`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id` bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`    bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `product_id`  bigint(20) DEFAULT NULL COMMENT '商品ID',
    `price`       decimal(10, 2) DEFAULT NULL COMMENT '价格',
    `stock`       int(11) DEFAULT '0' COMMENT '库存',
    `lock_stock`  int(11) DEFAULT '0' COMMENT '锁定库存',
    `pic`         varchar(256)   DEFAULT NULL COMMENT '图片',
    `attribute`   varchar(1024)  DEFAULT NULL COMMENT '属性，JSON 格式',
    `create_time` datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`    tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY           `idx_brand_id_product_id` (`brand_id`,`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477056338335555585 DEFAULT CHARSET=utf8mb4 COMMENT='商品库存表';

CREATE TABLE `product_sku_7`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id` bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`    bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `product_id`  bigint(20) DEFAULT NULL COMMENT '商品ID',
    `price`       decimal(10, 2) DEFAULT NULL COMMENT '价格',
    `stock`       int(11) DEFAULT '0' COMMENT '库存',
    `lock_stock`  int(11) DEFAULT '0' COMMENT '锁定库存',
    `pic`         varchar(256)   DEFAULT NULL COMMENT '图片',
    `attribute`   varchar(1024)  DEFAULT NULL COMMENT '属性，JSON 格式',
    `create_time` datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`    tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY           `idx_brand_id_product_id` (`brand_id`,`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477056338335555585 DEFAULT CHARSET=utf8mb4 COMMENT='商品库存表';

CREATE TABLE `product_sku_8`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id` bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`    bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `product_id`  bigint(20) DEFAULT NULL COMMENT '商品ID',
    `price`       decimal(10, 2) DEFAULT NULL COMMENT '价格',
    `stock`       int(11) DEFAULT '0' COMMENT '库存',
    `lock_stock`  int(11) DEFAULT '0' COMMENT '锁定库存',
    `pic`         varchar(256)   DEFAULT NULL COMMENT '图片',
    `attribute`   varchar(1024)  DEFAULT NULL COMMENT '属性，JSON 格式',
    `create_time` datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`    tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY           `idx_brand_id_product_id` (`brand_id`,`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477056338335555585 DEFAULT CHARSET=utf8mb4 COMMENT='商品库存表';

CREATE TABLE `product_sku_9`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id` bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`    bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `product_id`  bigint(20) DEFAULT NULL COMMENT '商品ID',
    `price`       decimal(10, 2) DEFAULT NULL COMMENT '价格',
    `stock`       int(11) DEFAULT '0' COMMENT '库存',
    `lock_stock`  int(11) DEFAULT '0' COMMENT '锁定库存',
    `pic`         varchar(256)   DEFAULT NULL COMMENT '图片',
    `attribute`   varchar(1024)  DEFAULT NULL COMMENT '属性，JSON 格式',
    `create_time` datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`    tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY           `idx_brand_id_product_id` (`brand_id`,`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477056338335555585 DEFAULT CHARSET=utf8mb4 COMMENT='商品库存表';

CREATE TABLE `product_spu_0`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id`          bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`             bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `name`                 varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_sn`           varchar(128)   DEFAULT NULL COMMENT '商品编码',
    `pic`                  varchar(256)   DEFAULT NULL COMMENT '商品主图',
    `photo_album`          text COMMENT '商品图集',
    `price`                decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `promotion_price`      decimal(10, 2) DEFAULT NULL COMMENT '促销价格',
    `promotion_start_time` datetime       DEFAULT NULL COMMENT '促销开始时间',
    `promotion_end_time`   datetime       DEFAULT NULL COMMENT '促销结束时间',
    `sub_title`            varchar(64)    DEFAULT NULL COMMENT '副标题',
    `sales`                int(11) DEFAULT '0' COMMENT '销量',
    `unit`                 varchar(32)    DEFAULT NULL COMMENT '单位',
    `detail`               text COMMENT '商品详情',
    `publish_status`       tinyint(1) DEFAULT NULL COMMENT '发布状态 0：发布 1：未发布',
    `new_status`           tinyint(1) DEFAULT NULL COMMENT '新品状态 0：新品 1：非新品',
    `recommand_status`     tinyint(1) DEFAULT NULL COMMENT '推荐状态 0：推荐 1：非推荐',
    `create_time`          datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`          datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`             tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                    `idx_brand_id` (`brand_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1634852818433081345 DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

CREATE TABLE `product_spu_1`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id`          bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`             bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `name`                 varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_sn`           varchar(128)   DEFAULT NULL COMMENT '商品编码',
    `pic`                  varchar(256)   DEFAULT NULL COMMENT '商品主图',
    `photo_album`          text COMMENT '商品图集',
    `price`                decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `promotion_price`      decimal(10, 2) DEFAULT NULL COMMENT '促销价格',
    `promotion_start_time` datetime       DEFAULT NULL COMMENT '促销开始时间',
    `promotion_end_time`   datetime       DEFAULT NULL COMMENT '促销结束时间',
    `sub_title`            varchar(64)    DEFAULT NULL COMMENT '副标题',
    `sales`                int(11) DEFAULT '0' COMMENT '销量',
    `unit`                 varchar(32)    DEFAULT NULL COMMENT '单位',
    `detail`               text COMMENT '商品详情',
    `publish_status`       tinyint(1) DEFAULT NULL COMMENT '发布状态 0：发布 1：未发布',
    `new_status`           tinyint(1) DEFAULT NULL COMMENT '新品状态 0：新品 1：非新品',
    `recommand_status`     tinyint(1) DEFAULT NULL COMMENT '推荐状态 0：推荐 1：非推荐',
    `create_time`          datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`          datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`             tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                    `idx_brand_id` (`brand_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477055850256982017 DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

CREATE TABLE `product_spu_10`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id`          bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`             bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `name`                 varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_sn`           varchar(128)   DEFAULT NULL COMMENT '商品编码',
    `pic`                  varchar(256)   DEFAULT NULL COMMENT '商品主图',
    `photo_album`          text COMMENT '商品图集',
    `price`                decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `promotion_price`      decimal(10, 2) DEFAULT NULL COMMENT '促销价格',
    `promotion_start_time` datetime       DEFAULT NULL COMMENT '促销开始时间',
    `promotion_end_time`   datetime       DEFAULT NULL COMMENT '促销结束时间',
    `sub_title`            varchar(64)    DEFAULT NULL COMMENT '副标题',
    `sales`                int(11) DEFAULT '0' COMMENT '销量',
    `unit`                 varchar(32)    DEFAULT NULL COMMENT '单位',
    `detail`               text COMMENT '商品详情',
    `publish_status`       tinyint(1) DEFAULT NULL COMMENT '发布状态 0：发布 1：未发布',
    `new_status`           tinyint(1) DEFAULT NULL COMMENT '新品状态 0：新品 1：非新品',
    `recommand_status`     tinyint(1) DEFAULT NULL COMMENT '推荐状态 0：推荐 1：非推荐',
    `create_time`          datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`          datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`             tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                    `idx_brand_id` (`brand_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477055850256982017 DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

CREATE TABLE `product_spu_11`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id`          bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`             bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `name`                 varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_sn`           varchar(128)   DEFAULT NULL COMMENT '商品编码',
    `pic`                  varchar(256)   DEFAULT NULL COMMENT '商品主图',
    `photo_album`          text COMMENT '商品图集',
    `price`                decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `promotion_price`      decimal(10, 2) DEFAULT NULL COMMENT '促销价格',
    `promotion_start_time` datetime       DEFAULT NULL COMMENT '促销开始时间',
    `promotion_end_time`   datetime       DEFAULT NULL COMMENT '促销结束时间',
    `sub_title`            varchar(64)    DEFAULT NULL COMMENT '副标题',
    `sales`                int(11) DEFAULT '0' COMMENT '销量',
    `unit`                 varchar(32)    DEFAULT NULL COMMENT '单位',
    `detail`               text COMMENT '商品详情',
    `publish_status`       tinyint(1) DEFAULT NULL COMMENT '发布状态 0：发布 1：未发布',
    `new_status`           tinyint(1) DEFAULT NULL COMMENT '新品状态 0：新品 1：非新品',
    `recommand_status`     tinyint(1) DEFAULT NULL COMMENT '推荐状态 0：推荐 1：非推荐',
    `create_time`          datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`          datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`             tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                    `idx_brand_id` (`brand_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477055850256982017 DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

CREATE TABLE `product_spu_12`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id`          bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`             bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `name`                 varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_sn`           varchar(128)   DEFAULT NULL COMMENT '商品编码',
    `pic`                  varchar(256)   DEFAULT NULL COMMENT '商品主图',
    `photo_album`          text COMMENT '商品图集',
    `price`                decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `promotion_price`      decimal(10, 2) DEFAULT NULL COMMENT '促销价格',
    `promotion_start_time` datetime       DEFAULT NULL COMMENT '促销开始时间',
    `promotion_end_time`   datetime       DEFAULT NULL COMMENT '促销结束时间',
    `sub_title`            varchar(64)    DEFAULT NULL COMMENT '副标题',
    `sales`                int(11) DEFAULT '0' COMMENT '销量',
    `unit`                 varchar(32)    DEFAULT NULL COMMENT '单位',
    `detail`               text COMMENT '商品详情',
    `publish_status`       tinyint(1) DEFAULT NULL COMMENT '发布状态 0：发布 1：未发布',
    `new_status`           tinyint(1) DEFAULT NULL COMMENT '新品状态 0：新品 1：非新品',
    `recommand_status`     tinyint(1) DEFAULT NULL COMMENT '推荐状态 0：推荐 1：非推荐',
    `create_time`          datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`          datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`             tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                    `idx_brand_id` (`brand_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477055850256982017 DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

CREATE TABLE `product_spu_13`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id`          bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`             bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `name`                 varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_sn`           varchar(128)   DEFAULT NULL COMMENT '商品编码',
    `pic`                  varchar(256)   DEFAULT NULL COMMENT '商品主图',
    `photo_album`          text COMMENT '商品图集',
    `price`                decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `promotion_price`      decimal(10, 2) DEFAULT NULL COMMENT '促销价格',
    `promotion_start_time` datetime       DEFAULT NULL COMMENT '促销开始时间',
    `promotion_end_time`   datetime       DEFAULT NULL COMMENT '促销结束时间',
    `sub_title`            varchar(64)    DEFAULT NULL COMMENT '副标题',
    `sales`                int(11) DEFAULT '0' COMMENT '销量',
    `unit`                 varchar(32)    DEFAULT NULL COMMENT '单位',
    `detail`               text COMMENT '商品详情',
    `publish_status`       tinyint(1) DEFAULT NULL COMMENT '发布状态 0：发布 1：未发布',
    `new_status`           tinyint(1) DEFAULT NULL COMMENT '新品状态 0：新品 1：非新品',
    `recommand_status`     tinyint(1) DEFAULT NULL COMMENT '推荐状态 0：推荐 1：非推荐',
    `create_time`          datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`          datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`             tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                    `idx_brand_id` (`brand_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477055850256982017 DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

CREATE TABLE `product_spu_14`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id`          bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`             bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `name`                 varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_sn`           varchar(128)   DEFAULT NULL COMMENT '商品编码',
    `pic`                  varchar(256)   DEFAULT NULL COMMENT '商品主图',
    `photo_album`          text COMMENT '商品图集',
    `price`                decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `promotion_price`      decimal(10, 2) DEFAULT NULL COMMENT '促销价格',
    `promotion_start_time` datetime       DEFAULT NULL COMMENT '促销开始时间',
    `promotion_end_time`   datetime       DEFAULT NULL COMMENT '促销结束时间',
    `sub_title`            varchar(64)    DEFAULT NULL COMMENT '副标题',
    `sales`                int(11) DEFAULT '0' COMMENT '销量',
    `unit`                 varchar(32)    DEFAULT NULL COMMENT '单位',
    `detail`               text COMMENT '商品详情',
    `publish_status`       tinyint(1) DEFAULT NULL COMMENT '发布状态 0：发布 1：未发布',
    `new_status`           tinyint(1) DEFAULT NULL COMMENT '新品状态 0：新品 1：非新品',
    `recommand_status`     tinyint(1) DEFAULT NULL COMMENT '推荐状态 0：推荐 1：非推荐',
    `create_time`          datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`          datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`             tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                    `idx_brand_id` (`brand_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477055850256982017 DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

CREATE TABLE `product_spu_15`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id`          bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`             bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `name`                 varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_sn`           varchar(128)   DEFAULT NULL COMMENT '商品编码',
    `pic`                  varchar(256)   DEFAULT NULL COMMENT '商品主图',
    `photo_album`          text COMMENT '商品图集',
    `price`                decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `promotion_price`      decimal(10, 2) DEFAULT NULL COMMENT '促销价格',
    `promotion_start_time` datetime       DEFAULT NULL COMMENT '促销开始时间',
    `promotion_end_time`   datetime       DEFAULT NULL COMMENT '促销结束时间',
    `sub_title`            varchar(64)    DEFAULT NULL COMMENT '副标题',
    `sales`                int(11) DEFAULT '0' COMMENT '销量',
    `unit`                 varchar(32)    DEFAULT NULL COMMENT '单位',
    `detail`               text COMMENT '商品详情',
    `publish_status`       tinyint(1) DEFAULT NULL COMMENT '发布状态 0：发布 1：未发布',
    `new_status`           tinyint(1) DEFAULT NULL COMMENT '新品状态 0：新品 1：非新品',
    `recommand_status`     tinyint(1) DEFAULT NULL COMMENT '推荐状态 0：推荐 1：非推荐',
    `create_time`          datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`          datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`             tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                    `idx_brand_id` (`brand_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477055850256982017 DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

CREATE TABLE `product_spu_2`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id`          bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`             bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `name`                 varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_sn`           varchar(128)   DEFAULT NULL COMMENT '商品编码',
    `pic`                  varchar(256)   DEFAULT NULL COMMENT '商品主图',
    `photo_album`          text COMMENT '商品图集',
    `price`                decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `promotion_price`      decimal(10, 2) DEFAULT NULL COMMENT '促销价格',
    `promotion_start_time` datetime       DEFAULT NULL COMMENT '促销开始时间',
    `promotion_end_time`   datetime       DEFAULT NULL COMMENT '促销结束时间',
    `sub_title`            varchar(64)    DEFAULT NULL COMMENT '副标题',
    `sales`                int(11) DEFAULT '0' COMMENT '销量',
    `unit`                 varchar(32)    DEFAULT NULL COMMENT '单位',
    `detail`               text COMMENT '商品详情',
    `publish_status`       tinyint(1) DEFAULT NULL COMMENT '发布状态 0：发布 1：未发布',
    `new_status`           tinyint(1) DEFAULT NULL COMMENT '新品状态 0：新品 1：非新品',
    `recommand_status`     tinyint(1) DEFAULT NULL COMMENT '推荐状态 0：推荐 1：非推荐',
    `create_time`          datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`          datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`             tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                    `idx_brand_id` (`brand_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477055850256982017 DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

CREATE TABLE `product_spu_3`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id`          bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`             bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `name`                 varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_sn`           varchar(128)   DEFAULT NULL COMMENT '商品编码',
    `pic`                  varchar(256)   DEFAULT NULL COMMENT '商品主图',
    `photo_album`          text COMMENT '商品图集',
    `price`                decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `promotion_price`      decimal(10, 2) DEFAULT NULL COMMENT '促销价格',
    `promotion_start_time` datetime       DEFAULT NULL COMMENT '促销开始时间',
    `promotion_end_time`   datetime       DEFAULT NULL COMMENT '促销结束时间',
    `sub_title`            varchar(64)    DEFAULT NULL COMMENT '副标题',
    `sales`                int(11) DEFAULT '0' COMMENT '销量',
    `unit`                 varchar(32)    DEFAULT NULL COMMENT '单位',
    `detail`               text COMMENT '商品详情',
    `publish_status`       tinyint(1) DEFAULT NULL COMMENT '发布状态 0：发布 1：未发布',
    `new_status`           tinyint(1) DEFAULT NULL COMMENT '新品状态 0：新品 1：非新品',
    `recommand_status`     tinyint(1) DEFAULT NULL COMMENT '推荐状态 0：推荐 1：非推荐',
    `create_time`          datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`          datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`             tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                    `idx_brand_id` (`brand_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477055850256982017 DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

CREATE TABLE `product_spu_4`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id`          bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`             bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `name`                 varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_sn`           varchar(128)   DEFAULT NULL COMMENT '商品编码',
    `pic`                  varchar(256)   DEFAULT NULL COMMENT '商品主图',
    `photo_album`          text COMMENT '商品图集',
    `price`                decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `promotion_price`      decimal(10, 2) DEFAULT NULL COMMENT '促销价格',
    `promotion_start_time` datetime       DEFAULT NULL COMMENT '促销开始时间',
    `promotion_end_time`   datetime       DEFAULT NULL COMMENT '促销结束时间',
    `sub_title`            varchar(64)    DEFAULT NULL COMMENT '副标题',
    `sales`                int(11) DEFAULT '0' COMMENT '销量',
    `unit`                 varchar(32)    DEFAULT NULL COMMENT '单位',
    `detail`               text COMMENT '商品详情',
    `publish_status`       tinyint(1) DEFAULT NULL COMMENT '发布状态 0：发布 1：未发布',
    `new_status`           tinyint(1) DEFAULT NULL COMMENT '新品状态 0：新品 1：非新品',
    `recommand_status`     tinyint(1) DEFAULT NULL COMMENT '推荐状态 0：推荐 1：非推荐',
    `create_time`          datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`          datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`             tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                    `idx_brand_id` (`brand_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477055850256982017 DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

CREATE TABLE `product_spu_5`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id`          bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`             bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `name`                 varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_sn`           varchar(128)   DEFAULT NULL COMMENT '商品编码',
    `pic`                  varchar(256)   DEFAULT NULL COMMENT '商品主图',
    `photo_album`          text COMMENT '商品图集',
    `price`                decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `promotion_price`      decimal(10, 2) DEFAULT NULL COMMENT '促销价格',
    `promotion_start_time` datetime       DEFAULT NULL COMMENT '促销开始时间',
    `promotion_end_time`   datetime       DEFAULT NULL COMMENT '促销结束时间',
    `sub_title`            varchar(64)    DEFAULT NULL COMMENT '副标题',
    `sales`                int(11) DEFAULT '0' COMMENT '销量',
    `unit`                 varchar(32)    DEFAULT NULL COMMENT '单位',
    `detail`               text COMMENT '商品详情',
    `publish_status`       tinyint(1) DEFAULT NULL COMMENT '发布状态 0：发布 1：未发布',
    `new_status`           tinyint(1) DEFAULT NULL COMMENT '新品状态 0：新品 1：非新品',
    `recommand_status`     tinyint(1) DEFAULT NULL COMMENT '推荐状态 0：推荐 1：非推荐',
    `create_time`          datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`          datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`             tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                    `idx_brand_id` (`brand_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477055850256982017 DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

CREATE TABLE `product_spu_6`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id`          bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`             bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `name`                 varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_sn`           varchar(128)   DEFAULT NULL COMMENT '商品编码',
    `pic`                  varchar(256)   DEFAULT NULL COMMENT '商品主图',
    `photo_album`          text COMMENT '商品图集',
    `price`                decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `promotion_price`      decimal(10, 2) DEFAULT NULL COMMENT '促销价格',
    `promotion_start_time` datetime       DEFAULT NULL COMMENT '促销开始时间',
    `promotion_end_time`   datetime       DEFAULT NULL COMMENT '促销结束时间',
    `sub_title`            varchar(64)    DEFAULT NULL COMMENT '副标题',
    `sales`                int(11) DEFAULT '0' COMMENT '销量',
    `unit`                 varchar(32)    DEFAULT NULL COMMENT '单位',
    `detail`               text COMMENT '商品详情',
    `publish_status`       tinyint(1) DEFAULT NULL COMMENT '发布状态 0：发布 1：未发布',
    `new_status`           tinyint(1) DEFAULT NULL COMMENT '新品状态 0：新品 1：非新品',
    `recommand_status`     tinyint(1) DEFAULT NULL COMMENT '推荐状态 0：推荐 1：非推荐',
    `create_time`          datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`          datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`             tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                    `idx_brand_id` (`brand_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477055850256982017 DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

CREATE TABLE `product_spu_7`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id`          bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`             bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `name`                 varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_sn`           varchar(128)   DEFAULT NULL COMMENT '商品编码',
    `pic`                  varchar(256)   DEFAULT NULL COMMENT '商品主图',
    `photo_album`          text COMMENT '商品图集',
    `price`                decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `promotion_price`      decimal(10, 2) DEFAULT NULL COMMENT '促销价格',
    `promotion_start_time` datetime       DEFAULT NULL COMMENT '促销开始时间',
    `promotion_end_time`   datetime       DEFAULT NULL COMMENT '促销结束时间',
    `sub_title`            varchar(64)    DEFAULT NULL COMMENT '副标题',
    `sales`                int(11) DEFAULT '0' COMMENT '销量',
    `unit`                 varchar(32)    DEFAULT NULL COMMENT '单位',
    `detail`               text COMMENT '商品详情',
    `publish_status`       tinyint(1) DEFAULT NULL COMMENT '发布状态 0：发布 1：未发布',
    `new_status`           tinyint(1) DEFAULT NULL COMMENT '新品状态 0：新品 1：非新品',
    `recommand_status`     tinyint(1) DEFAULT NULL COMMENT '推荐状态 0：推荐 1：非推荐',
    `create_time`          datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`          datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`             tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                    `idx_brand_id` (`brand_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477055850256982017 DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

CREATE TABLE `product_spu_8`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id`          bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`             bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `name`                 varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_sn`           varchar(128)   DEFAULT NULL COMMENT '商品编码',
    `pic`                  varchar(256)   DEFAULT NULL COMMENT '商品主图',
    `photo_album`          text COMMENT '商品图集',
    `price`                decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `promotion_price`      decimal(10, 2) DEFAULT NULL COMMENT '促销价格',
    `promotion_start_time` datetime       DEFAULT NULL COMMENT '促销开始时间',
    `promotion_end_time`   datetime       DEFAULT NULL COMMENT '促销结束时间',
    `sub_title`            varchar(64)    DEFAULT NULL COMMENT '副标题',
    `sales`                int(11) DEFAULT '0' COMMENT '销量',
    `unit`                 varchar(32)    DEFAULT NULL COMMENT '单位',
    `detail`               text COMMENT '商品详情',
    `publish_status`       tinyint(1) DEFAULT NULL COMMENT '发布状态 0：发布 1：未发布',
    `new_status`           tinyint(1) DEFAULT NULL COMMENT '新品状态 0：新品 1：非新品',
    `recommand_status`     tinyint(1) DEFAULT NULL COMMENT '推荐状态 0：推荐 1：非推荐',
    `create_time`          datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`          datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`             tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                    `idx_brand_id` (`brand_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477055850256982017 DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

CREATE TABLE `product_spu_9`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id`          bigint(20) DEFAULT NULL COMMENT '商品类型ID',
    `brand_id`             bigint(20) DEFAULT NULL COMMENT '商品品牌ID',
    `name`                 varchar(256)   DEFAULT NULL COMMENT '商品名称',
    `product_sn`           varchar(128)   DEFAULT NULL COMMENT '商品编码',
    `pic`                  varchar(256)   DEFAULT NULL COMMENT '商品主图',
    `photo_album`          text COMMENT '商品图集',
    `price`                decimal(10, 2) DEFAULT NULL COMMENT '商品价格',
    `promotion_price`      decimal(10, 2) DEFAULT NULL COMMENT '促销价格',
    `promotion_start_time` datetime       DEFAULT NULL COMMENT '促销开始时间',
    `promotion_end_time`   datetime       DEFAULT NULL COMMENT '促销结束时间',
    `sub_title`            varchar(64)    DEFAULT NULL COMMENT '副标题',
    `sales`                int(11) DEFAULT '0' COMMENT '销量',
    `unit`                 varchar(32)    DEFAULT NULL COMMENT '单位',
    `detail`               text COMMENT '商品详情',
    `publish_status`       tinyint(1) DEFAULT NULL COMMENT '发布状态 0：发布 1：未发布',
    `new_status`           tinyint(1) DEFAULT NULL COMMENT '新品状态 0：新品 1：非新品',
    `recommand_status`     tinyint(1) DEFAULT NULL COMMENT '推荐状态 0：推荐 1：非推荐',
    `create_time`          datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`          datetime       DEFAULT NULL COMMENT '修改时间',
    `del_flag`             tinyint(1) DEFAULT NULL COMMENT '删除标记 0：未删除 1：删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`),
    KEY                    `idx_brand_id` (`brand_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1477055850256982017 DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

CREATE TABLE `undo_log`
(
    `branch_id`     bigint(20) NOT NULL COMMENT 'branch transaction id',
    `xid`           varchar(128) NOT NULL COMMENT 'global transaction id',
    `context`       varchar(128) NOT NULL COMMENT 'undo_log context,such as serialization',
    `rollback_info` longblob     NOT NULL COMMENT 'rollback info',
    `log_status`    int(11) NOT NULL COMMENT '0:normal status,1:defense status',
    `log_created`   datetime(6) NOT NULL COMMENT 'create datetime',
    `log_modified`  datetime(6) NOT NULL COMMENT 'modify datetime',
    UNIQUE KEY `ux_undo_log` (`xid`,`branch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AT transaction mode undo table';

INSERT INTO `product_attribute` (`id`, `product_attribute_category_id`, `name`, `option_status`, `option_list`, `sort`,
                                 `type`, `create_time`, `update_time`, `del_flag`)
VALUES ('1477055926719143936', '1477056215689912320', '尺寸', '1', 'M,X,XL,2XL,3XL,4XL,5XL,6XL,7XL', '0', '0',
        '2022-09-14 21:03:34', '2022-09-14 21:03:34', '0'),
       ('1477056027457937408', '1477055926513623040', '外观', '0', NULL, '0', '0', '2022-09-14 21:32:43',
        '2022-09-14 21:32:43', '0'),
       ('1477056202448494592', '1477055926513623040', '版本', '0', NULL, '1', '0', '2022-09-14 21:32:43',
        '2022-09-14 21:32:43', '0');

INSERT INTO `product_attribute_category` (`id`, `name`, `create_time`, `update_time`, `del_flag`)
VALUES ('1477055926513623040', '手机数码', '2022-09-14 21:02:39', '2022-09-14 21:02:39', '0'),
       ('1477056215689912320', '服装', '2022-09-14 21:02:39', '2022-09-14 21:02:39', '0');

INSERT INTO `product_attribute_value` (`id`, `product_id`, `product_attribute_id`, `attribute_value`, `create_time`,
                                       `update_time`, `del_flag`)
VALUES ('1477056027441160192', '1477055850256982016', '1477056027457937408', '月影黑,星耀金', '2022-09-14 21:33:36',
        '2022-09-14 21:33:36', '0'),
       ('1477056215706689536', '1477055850256982016', '1477056202448494592', '12GB+256GB,12GB+512GB',
        '2022-09-14 21:33:36', '2022-09-14 21:33:36', '0');

INSERT INTO `product_brand` (`id`, `name`, `desc`, `pic`, `sort`, `create_time`, `update_time`, `del_flag`)
VALUES ('1477055818267025408', '小米',
        '小米是一家以手机、智能硬件和IoT平台为核心的互联网公司，以智能手机、智能电视、笔记本等丰富的产品与服务。致力于让全球每个人都能享受科技带来的美好生活',
        NULL, '1', '2022-09-14 21:01:59', '2022-09-14 21:01:59', '0'),
       ('1477055850177290224', '刚果云店', '刚果商城示例网站销售商品', NULL, '0', '2022-09-14 21:01:59',
        '2022-09-14 21:01:59', '0');

INSERT INTO `product_category` (`id`, `name`, `parent_id`, `level`, `icon_url`, `sort`, `url`, `status`, `create_time`,
                                `update_time`, `del_flag`)
VALUES ('1477055818388660224', '手机数码', '0', '0', NULL, '0', NULL, '0', '2022-09-14 21:02:50', '2022-09-14 21:02:50',
        '0'),
       ('1477055850177290240', '手机配件', '1477055818388660224', '1', NULL, '0', NULL, '0', '2022-09-14 21:02:50',
        '2022-09-14 21:02:50', '0');

INSERT INTO `product_sku_0` (`id`, `category_id`, `brand_id`, `product_id`, `price`, `stock`, `lock_stock`, `pic`,
                             `attribute`, `create_time`, `update_time`, `del_flag`)
VALUES ('1477056250724933632', '1477055818388660224', '1477055818267025408', '1634852818433081344', '9999.00', '999897',
        '576', '', '[{\"key\":\"外观\",\"value\":\"月影黑\"},{\"key\":\"版本\",\"value\":\"12GB+256GB\"}]',
        '2022-09-15 20:06:47', '2022-09-15 20:06:47', '0'),
       ('1477056250741710848', '1477055818388660224', '1477055818267025408', '1634852818433081344', '11999.00', '299',
        '5', '', '[{\"key\":\"外观\",\"value\":\"月影黑\"},{\"key\":\"版本\",\"value\":\"12GB+512GB\"}]',
        '2022-09-15 20:06:47', '2022-09-15 20:06:47', '0'),
       ('1477056338104868864', '1477055818388660224', '1477055818267025408', '1634852818433081344', '9999.00', '188',
        '2', '', '[{\"key\":\"外观\",\"value\":\"星耀金\"},{\"key\":\"版本\",\"value\":\"12GB+256GB\"}]',
        '2022-09-15 20:06:47', '2022-09-15 20:06:47', '0'),
       ('1477056338335555584', '1477055818388660224', '1477055818267025408', '1634852818433081344', '11999.00', '299',
        '4', '', '[{\"key\":\"外观\",\"value\":\"星耀金\"},{\"key\":\"版本\",\"value\":\"12GB+512GB\"}]',
        '2022-09-15 20:06:47', '2022-09-15 20:06:47', '0'),
       ('1646908225162838016', '1477055818388660224', '1477055850177290224', '1647581868712787968', '1.00', '99999998',
        '1', 'https://ooo.0o0.ooo/2019/09/30/CAZ6QrXPBoh5aIT.png', NULL, '2022-09-15 20:06:47', '2022-09-15 20:06:47',
        '0'),
       ('1647774900053606400', '1477055818388660224', '1477055850177290224', '1647582624404733952', '1.00', '99999997',
        '2', 'https://ooo.0o0.ooo/2018/11/04/5bdeba4028e90.png', NULL, '2022-09-15 20:06:47', '2022-09-15 20:06:47',
        '0'),
       ('1647775798293168128', '1477055818388660224', '1477055850177290224', '1647582962360778752', '1.00', '99999999',
        '0', 'https://s1.ax1x.com/2018/05/19/Ccdiid.png', NULL, '2022-09-15 20:06:47', '2022-09-15 20:06:47', '0'),
       ('1647775907500261376', '1477055818388660224', '1477055850177290224', '1647594323929202688', '1.00', '99999999',
        '0', 'https://resource.smartisan.com/resource/6/610400xinpinpeijian.jpg', NULL, '2022-09-15 20:06:47',
        '2022-09-15 20:06:47', '0'),
       ('1647776007962230784', '1477055818388660224', '1477055850177290224', '1647595197841801216', '1.00', '99999999',
        '0', 'https://resource.smartisan.com/resource/6/610400yijiuhuanxin.jpg', NULL, '2022-09-15 20:06:47',
        '2022-09-15 20:06:47', '0'),
       ('1647776154976780288', '1477055818388660224', '1477055850177290224', '1647595620153688064', '1.00', '99999999',
        '0', 'https://resource.smartisan.com/resource/4/489673079577637073.png', NULL, '2022-09-15 20:06:47',
        '2022-09-15 20:06:47', '0'),
       ('1647776452193550336', '1477055818388660224', '1477055850177290224', '1647598377245868032', '1.00', '99999999',
        '0', 'https://resource.smartisan.com/resource/fe6ab43348a43152b4001b4454d206ac.jpg', NULL,
        '2022-09-15 20:06:47', '2022-09-15 20:06:47', '0'),
       ('1647776550206046208', '1477055818388660224', '1477055850177290224', '1647772580494442496', '1.00', '99999999',
        '0', 'https://ooo.0o0.ooo/2018/07/13/5b48a7f468bf2.png', NULL, '2022-09-15 20:06:47', '2022-09-15 20:06:47',
        '0'),
       ('1647776637426597888', '1477055818388660224', '1477055850177290224', '1647774302277206016', '1.00', '99999995',
        '4', 'https://ooo.0o0.ooo/2020/07/24/6BV9uTwaqModbYL.png', NULL, '2022-09-15 20:06:47', '2022-09-15 20:06:47',
        '0'),
       ('1647778488314232832', '1477055818388660224', '1477055850177290224', '1647777981810081792', '1999.00',
        '99999999', '0', 'https://resource.smartisan.com/resource/1/1220858shoujilouceng.jpg', NULL,
        '2022-09-15 20:06:47', '2022-09-15 20:06:47', '0'),
       ('1647779189778022400', '1477055818388660224', '1477055850177290224', '1647778797803536384', '49.00', '99999999',
        '0', 'https://resource.smartisan.com/resource/1/1220858shoujilouceng.jpg', NULL, '2022-09-15 20:06:47',
        '2022-09-15 20:06:47', '0'),
       ('1647779679454625792', '1477055818388660224', '1477055850177290224', '1647779363573202944', '79.00', '99999999',
        '0', 'https://resource.smartisan.com/resource/63ea40e5c64db1c6b1d480c48fe01272.jpg', NULL,
        '2022-09-15 20:06:47', '2022-09-15 20:06:47', '0'),
       ('1647782832447160320', '1477055818388660224', '1477055850177290224', '1647782365793091584', '29.00', '99999999',
        '0', 'https://resource.smartisan.com/resource/5e4b1feddb13639550849f12f6b2e202.jpg', NULL,
        '2022-09-15 20:06:47', '2022-09-15 20:06:47', '0'),
       ('1647783635568295936', '1477055818388660224', '1477055850177290224', '1647783108105207808', '89.00', '99999999',
        '0', 'https://resource.smartisan.com/resource/10525c4b21f039fc8ccb42cd1586f5cd.jpg', NULL,
        '2022-09-15 20:06:47', '2022-09-15 20:06:47', '0'),
       ('1647784305067294720', '1477055818388660224', '1477055850177290224', '1647783876426203136', '49.00', '99999999',
        '0', 'https://resource.smartisan.com/resource/b899d9b82c4bc2710492a26af021d484.jpg', NULL,
        '2022-09-15 20:06:47', '2022-09-15 20:06:47', '0'),
       ('1647788875948490752', '1477055818388660224', '1477055850177290224', '1647788115844136960', '199.00',
        '99999999', '0', 'https://resource.smartisan.com/resource/z/zhoubianshangpin1220858web.jpg', NULL,
        '2022-09-15 20:06:47', '2022-09-15 20:06:47', '0'),
       ('1647789408759316480', '1477055818388660224', '1477055850177290224', '1647788969091399680', '199.00',
        '99999999', '0', 'https://resource.smartisan.com/resource/2f9a0f5f3dedf0ed813622003f1b287b.jpg', NULL,
        '2022-09-15 20:06:47', '2022-09-15 20:06:47', '0'),
       ('1647790536012070912', '1477055818388660224', '1477055850177290224', '1647790006267281408', '149.00',
        '99999999', '0', 'https://resource.smartisan.com/resource/2b05dbca9f5a4d0c1270123f42fb834c.jpg', NULL,
        '2022-09-15 20:06:47', '2022-09-15 20:06:47', '0'),
       ('1647791148015550464', '1477055818388660224', '1477055850177290224', '1647790707097731072', '149.00',
        '99999999', '0', 'https://resource.smartisan.com/resource/804edf579887b3e1fae4e20a379be5b5.png', NULL,
        '2022-09-15 20:06:47', '2022-09-15 20:06:47', '0'),
       ('1647791733594914816', '1477055818388660224', '1477055850177290224', '1647791225895387136', '199.00',
        '99999999', '0', 'https://resource.smartisan.com/resource/a1c53b5f12dd7ef790cadec0eaeaf466.jpg', NULL,
        '2022-09-15 20:06:47', '2022-09-15 20:06:47', '0'),
       ('1647792358038700032', '1477055818388660224', '1477055850177290224', '1647791976487059456', '249.00',
        '99999999', '0', 'https://resource.smartisan.com/resource/daa975651d6d700c0f886718c520ee19.jpg', NULL,
        '2022-09-15 20:06:47', '2022-09-15 20:06:47', '0'),
       ('1647792939679612928', '1477055818388660224', '1477055850177290224', '1647792528780427264', '9.90', '99999999',
        '0', 'https://resource.smartisan.com/resource/3973d009d182d8023bea6250b9a3959e.jpg', NULL,
        '2022-09-15 20:06:47', '2022-09-15 20:06:47', '0'),
       ('1647795105861468160', '1477055818388660224', '1477055850177290224', '1647794693754322944', '199.00',
        '99999999', '0', 'https://resource.smartisan.com/resource/a/acillouceng1220856.jpg', NULL,
        '2022-09-15 20:06:47', '2022-09-15 20:06:47', '0'),
       ('1647795957305180160', '1477055818388660224', '1477055850177290224', '1647795373797801984', '2699.00',
        '99999999', '0', 'https://resource.smartisan.com/resource/7ac3af5a92ad791c2b38bfe1e38ee334.jpg', NULL,
        '2022-09-15 20:06:47', '2022-09-15 20:06:47', '0'),
       ('1647796490120200192', '1477055818388660224', '1477055850177290224', '1647796110271447040', '2799.00',
        '99999999', '0', 'https://resource.smartisan.com/resource/41cb562a47d4831e199ed7e2057f3b61.jpg', NULL,
        '2022-09-15 20:06:47', '2022-09-15 20:06:47', '0'),
       ('1647797712638181376', '1477055818388660224', '1477055850177290224', '1647797092950736896', '1499.00',
        '99999999', '0', 'https://resource.smartisan.com/resource/99c548bfc9848a8c95f4e4f7f2bc1095.png', NULL,
        '2022-09-15 20:06:47', '2022-09-15 20:06:47', '0'),
       ('1647798216168570880', '1477055818388660224', '1477055850177290224', '1647797850492370944', '2999.00',
        '99999999', '0', 'https://resource.smartisan.com/resource/71432ad30288fb860a4389881069b874.png', NULL,
        '2022-09-15 20:06:47', '2022-09-15 20:06:47', '0'),
       ('1647798636945342464', '1477055818388660224', '1477055850177290224', '1647798317083525120', '1499.00',
        '99999999', '0', 'https://resource.smartisan.com/resource/804b82e4c05516e822667a23ee311f7c.jpg', NULL,
        '2022-09-15 20:06:47', '2022-09-15 20:06:47', '0'),
       ('1647799329550761984', '1477055818388660224', '1477055850177290224', '1647799053083213824', '499.00',
        '99999999', '0', 'https://resource.smartisan.com/resource/367d93db1d58f9f3505bc0ec18efaa44.jpg', NULL,
        '2022-09-15 20:06:47', '2022-09-15 20:06:47', '0'),
       ('1647801637168742400', '1477055818388660224', '1477055850177290224', '1647801029158240256', '1.00', '99999999',
        '0', 'https://ooo.0o0.ooo/2018/07/13/5b48ac7766d98.png', NULL, '2022-09-15 20:06:47', '2022-09-15 20:06:47',
        '0'),
       ('1647802007097966592', '1477055818388660224', '1477055850177290224', '1647801864239972352', '1.00', '99999999',
        '0',
        'https://ooo.0o0.ooo/2018/11/04/5bdeba4028e90.png,https://ooo.0o0.ooo/2018/11/04/5bdeba4028e90.png,https://ooo.0o0.ooo/2018/11/04/5bdebb109a29a.png,https://ooo.0o0.ooo/2018/11/04/5bdeba6753403.png',
        NULL, '2022-09-15 20:06:47', '2022-09-15 20:06:47', '0'),
       ('1647802314720804864', '1477055818388660224', '1477055850177290224', '1647802152980054016', '1.00', '99999999',
        '0', 'https://ooo.0o0.ooo/2018/07/13/5b48ac7766d98.png', NULL, '2022-09-15 20:06:47', '2022-09-15 20:06:47',
        '0'),
       ('1647802328746557440', '1477055818388660224', '1477055850177290224', '1647802174643634176', '1.00', '99999999',
        '0',
        'https://ooo.0o0.ooo/2018/11/04/5bdeba4028e90.png,https://ooo.0o0.ooo/2018/11/04/5bdeba4028e90.png,https://ooo.0o0.ooo/2018/11/04/5bdebb109a29a.png,https://ooo.0o0.ooo/2018/11/04/5bdeba6753403.png',
        NULL, '2022-09-15 20:06:47', '2022-09-15 20:06:47', '0'),
       ('1647802328746557456', '1477055818388660224', '1477055850177290224', '1647784417420115968', '79.00', '99999999',
        '0', 'https://resource.smartisan.com/resource/abb6986430536cd9365352b434f3c568.jpg', NULL,
        '2022-09-15 20:06:47', '2022-09-15 20:06:47', '0');

INSERT INTO `product_spu_0` (`id`, `category_id`, `brand_id`, `name`, `product_sn`, `pic`, `photo_album`, `price`,
                             `promotion_price`, `promotion_start_time`, `promotion_end_time`, `sub_title`, `sales`,
                             `unit`, `detail`, `publish_status`, `new_status`, `recommand_status`, `create_time`,
                             `update_time`, `del_flag`)
VALUES ('1634852818433081344', '1477055818388660224', '1477055818267025408',
        '小米MIX Fold2 轻薄折叠 骁龙8+旗舰处理器 徕卡光学镜头 自研微水滴形态转轴 12GB+512GB 月影黑 5G手机',
        '100033890205', '', NULL, '9999.00', NULL, '2022-09-14 21:02:26', '2022-09-14 21:02:26',
        '轻薄折叠机,整机轻至262g,展开厚度5.4mm,骁龙8+旗舰处理器!【点击抢购小米11Ultra】', '0', '件', NULL, '0', '0', '0',
        '2022-09-14 21:02:26', '2022-09-14 21:02:26', '1'),
       ('1647581868712787968', '1477055818388660224', '1477055850177290224', '支付测试商品 IPhone X 全面屏 全面绽放',
        '100033890207', 'https://ooo.0o0.ooo/2018/07/13/5b48ac7766d98.png',
        'https://ooo.0o0.ooo/2018/07/13/5b48ac7766d98.png,https://ooo.0o0.ooo/2018/07/13/5b48ac9135c5f.png,https://ooo.0o0.ooo/2018/07/13/5b48ac9c2be6c.png,https://ooo.0o0.ooo/2018/07/13/5b48aca99c8b6.png,https://ooo.0o0.ooo/2018/07/13/5b48a7f468bf2.png',
        '1.00', NULL, '2022-09-14 21:02:26', '2022-09-14 21:02:26', '此仅为支付测试商品 拍下不会发货', '0', '件',
        '<p style=\"text-align:center;\">\n	<img src=\"https://img30.360buyimg.com/popWaterMark/jfs/t7843/137/3005340945/124833/dc7c71f2/59b8ccd1N2bffd055.jpg\" alt=\"\" /><img src=\"https://img30.360buyimg.com/popWaterMark/jfs/t8764/314/1380452846/296346/d62490e2/59b8ccd1N96ce760d.jpg\" alt=\"\" /><img src=\"https://img30.360buyimg.com/popWaterMark/jfs/t8710/275/1373463301/363710/ebf00bff/59b8ccbaN2d563f74.jpg\" alt=\"\" /><img src=\"https://img30.360buyimg.com/popWaterMark/jfs/t8632/330/1390725687/229853/e56f9e1b/59b8ccd1N7b8b6bdb.jpg\" alt=\"\" /><img src=\"https://img30.360buyimg.com/popWaterMark/jfs/t9115/290/1376678976/488369/591760dc/59b8ccc6N1563a61b.jpg\" alt=\"\" /><img src=\"https://img30.360buyimg.com/popWaterMark/jfs/t8233/331/1431263348/183032/b875528c/59b8ccd1Ne7e633e3.jpg\" alt=\"\" /><img src=\"https://img30.360buyimg.com/popWaterMark/jfs/t8785/253/890847377/186916/c467a464/59b8ccd1N4551397c.jpg\" alt=\"\" /> <img src=\"https://img30.360buyimg.com/popWaterMark/jfs/t8728/276/1416802585/172158/1516ec08/59b8ccd1N95aae9c9.jpg\" alt=\"\" /> <img src=\"https://img30.360buyimg.com/popWaterMark/jfs/t9082/133/1223014275/307097/58f97021/59b8ccd2Nebfc633a.jpg\" alt=\"\" /><img src=\"https://img30.360buyimg.com/popWaterMark/jfs/t9052/275/1400615286/155643/1b0ecf44/59b8ccd2N46bd82bf.jpg\" alt=\"\" /> <img src=\"https://img30.360buyimg.com/popWaterMark/jfs/t9169/240/1361662217/193435/24ed9b93/59b8ccd4N03cec407.jpg\" alt=\"\" /> <img src=\"https://img30.360buyimg.com/popWaterMark/jfs/t7390/232/3008585906/285016/56cbb12/59b8ccd4Nc8434af8.jpg\" alt=\"\" /> \n</p>',
        '0', '0', '0', '2022-09-14 21:02:26', '2023-04-17 13:33:47', '0'),
       ('1647582624404733952', '1477055818388660224', '1477055850177290224', '捐赠商品', '100033890208',
        'https://ooo.0o0.ooo/2020/07/24/6BV9uTwaqModbYL.png', 'https://ooo.0o0.ooo/2020/07/24/6BV9uTwaqModbYL.png',
        '1.00', NULL, '2022-09-14 21:02:26', '2022-09-14 21:02:26', '您的捐赠将用于本站维护 给您带来更好的体验', '0',
        '件',
        '<br />\n<br />\n<br />\n<span style=\"font-size:18px;\"><strong>为什么要捐赠？</strong></span> <br />\n<br />\n<span style=\"font-size:18px;\">捐赠是国外用来支持开发者和资源贡献者的一种常见的方式。</span> <br />\n<br />\n<span style=\"font-size:18px;\">这些开发者不通过加入广告或者进行第三方推广获得收入，</span> <br />\n<br />\n<span style=\"font-size:18px;\">仅通过使用的用户自己主动捐赠来表达对开发者的感谢！</span> <br />\n<br />\n<span style=\"font-size:18px;\">当一个非盈利项目仅仅依靠兴趣的支撑，很难确定它还能走多远。</span> <br />\n<br />\n<span style=\"font-size:18px;\">所有的捐赠都将用于提升我的环境配置，维持网站的运行和提高我的积极性。</span> <br />\n<br />\n<span style=\"font-size:18px;\">这个渠道的存在并不意味着你必须捐赠。</span> <br />\n<br />\n<span style=\"font-size:18px;\">你也可以不做任何事。你的捐赠意味着你对我过去所做的表示感谢，而不是表达对未来的期望。</span> <br />\n<br />\n<span style=\"font-size:18px;\">但你的捐赠会提高我的积极性和设备配置让我努力把手头上的事做的更好。</span> <br />\n<br />\n<span style=\"font-size:18px;\">我会维护一份名单以感谢所有的捐赠者。正如我所说，捐赠是一个向我表示感谢的方式。</span> <br />\n<br />\n<p>\n	<span style=\"font-size:18px;color:#E53333;\">【捐赠方式】</span> \n</p>\n<p>\n	<span style=\"font-size:18px;\"><br />\n</span> \n</p>\n<p>\n	<span style=\"font-size:18px;color:#337FE5;\">1. 登录本商城添加商品至购物车结算，或随意购买商品</span> \n</p>\n<p>\n	<span style=\"font-size:18px;\"><br />\n</span> \n</p>\n<p>\n	<span style=\"font-size:18px;color:#337FE5;\">2. 支付订单时填写捐赠信息</span> \n</p>\n<p>\n	<span style=\"font-size:18px;\"><br />\n</span> \n</p>\n<p>\n	<span style=\"font-size:18px;color:#337FE5;\">3. 选择捐赠金额，当金额大于一定额度时会有相应文档或源码回赠</span> \n</p>\n<p>\n	<span style=\"font-size:18px;\"><br />\n</span> \n</p>\n<p>\n	<span style=\"font-size:18px;color:#E53333;\"><strong>登录商城任意购买商品，支付时填写捐赠信息</strong></span> \n</p>\n<br />\n<br />\n<br />\n<br />',
        '0', '0', '0', '2022-09-14 21:02:26', '2023-04-17 13:33:47', '0'),
       ('1647777981810081792', '1477055818388660224', '1477055850177290224', '坚果 3', '100033890215',
        'https://resource.smartisan.com/resource/1/1220858shoujilouceng.jpg',
        'https://resource.smartisan.com/resource/718bcecced0df1cd23bbdb9cc1f70b7d.png', '1999.00', NULL,
        '2022-09-14 21:02:26', '2022-09-14 21:02:26', '漂亮得不像实力派', '0', '件',
        '<img src=\"https://img20.360buyimg.com/vc/jfs/t17368/340/1617561606/1069487/9676971/5ad014b1Nb8463c4e.jpg\" width=\"1220px\" alt=\"\" />\n<img src=\"https://img20.360buyimg.com/vc/jfs/t17278/52/1620296383/1157116/3d0f473/5ad014b8N32c9c183.jpg\" width=\"1220px\" alt=\"\" />\n<img src=\"https://img20.360buyimg.com/vc/jfs/t19420/87/1609028179/1135327/9b3e0f97/5ad014c0N6234ba19.jpg\" width=\"1220px\" alt=\"\" />',
        '0', '0', '0', '2022-09-14 21:02:26', '2023-04-17 13:33:47', '0'),
       ('1647778797803536384', '1477055818388660224', '1477055850177290224', '坚果 3 前屏钢化玻璃保护膜',
        '100033890216', 'https://resource.smartisan.com/resource/3a7522290397a9444d7355298248f197.jpg',
        'https://resource.smartisan.com/resource/3a7522290397a9444d7355298248f197.jpg', '49.00', NULL,
        '2022-09-14 21:02:26', '2022-09-14 21:02:26', '超强透光率、耐刮花、防指纹', '0', '件',
        '<img src=\"https://resource.smartisan.com/resource/5dcbe27f36e1f8f2bfdfabb0b2681879.jpg\" style=\"width:1220px;height:3665px;\" alt=\"\" />',
        '0', '0', '0', '2022-09-14 21:02:26', '2023-04-17 13:33:47', '0'),
       ('1647779363573202944', '1477055818388660224', '1477055850177290224', '坚果 3 绒布国旗保护套', '100033890217',
        'https://resource.smartisan.com/resource/63ea40e5c64db1c6b1d480c48fe01272.jpg',
        'https://resource.smartisan.com/resource/63ea40e5c64db1c6b1d480c48fe01272.jpg,https://resource.smartisan.com/resource/4b8d00ab6ba508a977a475988e0fdb53.jpg,https://resource.smartisan.com/resource/87ea3888491d172b7d7a0e78e4294b4b.jpg,https://resource.smartisan.com/resource/8d30265188ddd1ba05e34f669c5dcf1c.jpg',
        '79.00', NULL, '2022-09-14 21:02:26', '2022-09-14 21:02:26', '超强透光率、耐刮花、防指纹', '0', '件',
        '<img src=\"https://resource.smartisan.com/resource/3aa27e8caf798b5e7e3796388190b43b.jpg\" style=\"width:1220px;height:5797px;\" alt=\"\" />',
        '0', '0', '0', '2022-09-14 21:02:26', '2023-04-17 13:33:47', '0'),
       ('1647782365793091584', '1477055818388660224', '1477055850177290224', '坚果 3 TPU 软胶透明保护套',
        '100033890218', 'https://resource.smartisan.com/resource/5e4b1feddb13639550849f12f6b2e202.jpg',
        'https://resource.smartisan.com/resource/5e4b1feddb13639550849f12f6b2e202.jpg,https://resource.smartisan.com/resource/0477d8b177db197a0b09a18e116b2bca.jpg,https://resource.smartisan.com/resource/b66d7e832339cf240b13a9a91107abdc.jpg,https://resource.smartisan.com/resource/56db3c83cca697572fa8a1df2e3172d7.jpg',
        '29.00', NULL, '2022-09-14 21:02:26', '2022-09-14 21:02:26', '轻薄透明、完美贴合、TPU 环保材质', '0', '件',
        '<img src=\"https://resource.smartisan.com/resource/fda5d962cc2b2e579c5df1c3d9e2f2c8.jpg\" style=\"width:1220px;height:4957px;\" alt=\"\" />',
        '0', '0', '0', '2022-09-14 21:02:26', '2023-04-17 13:33:47', '0'),
       ('1647783108105207808', '1477055818388660224', '1477055850177290224', 'Smartisan 半入耳式耳机', '100033890219',
        'https://resource.smartisan.com/resource/10525c4b21f039fc8ccb42cd1586f5cd.jpg',
        'https://resource.smartisan.com/resource/9c1d958f10a811df841298d50e1ca7c0.jpg,https://resource.smartisan.com/resource/afa4ecdb54d7f50d0b6265bbcf31d6c8.jpg,https://resource.smartisan.com/resource/eb1bf1dee365c7855e6b047d8b9c5b1e.jpg,https://resource.smartisan.com/resource/dfcc9fa16ab354a41683959398bff128.jpg',
        '89.00', NULL, '2022-09-14 21:02:26', '2022-09-14 21:02:26', '经典配色、专业调音、高品质麦克风', '0', '件',
        '<img src=\"https://resource.smartisan.com/resource/14497b77e21fc6d0807e57bb9deabe28.jpg\" style=\"width:1220px;height:9527px;\" alt=\"\" />',
        '0', '0', '0', '2022-09-14 21:02:26', '2023-04-17 13:33:47', '0'),
       ('1647783876426203136', '1477055818388660224', '1477055850177290224', '坚果 3 TPU 软胶保护套', '100033890220',
        'https://resource.smartisan.com/resource/b899d9b82c4bc2710492a26af021d484.jpg',
        'https://resource.smartisan.com/resource/b899d9b82c4bc2710492a26af021d484.jpg,https://resource.smartisan.com/resource/bb8859032d6060ccb4e733a2c8e2f51d.jpg,https://resource.smartisan.com/resource/df5b3d3539481eb1c00333a5bc5b60b6.jpg',
        '49.00', NULL, '2022-09-14 21:02:26', '2022-09-14 21:02:26', 'TPU 环保材质、完美贴合、周到防护', '0', '件',
        '<img src=\"https://resource.smartisan.com/resource/4272e7737eed967a8f2f10ebef9b84dc.jpg style=\"width:1220px;height:4990px;\" alt=\"\" />',
        '0', '0', '0', '2022-09-14 21:02:26', '2023-04-17 13:33:47', '0'),
       ('1647784417420115968', '1477055818388660224', '1477055850177290224', '坚果 3 \"足迹\"背贴 乐高创始人出生',
        '100033890221', 'https://resource.smartisan.com/resource/abb6986430536cd9365352b434f3c568.jpg',
        'https://resource.smartisan.com/resource/abb6986430536cd9365352b434f3c568.jpg', '79.00', NULL,
        '2022-09-14 21:02:26', '2022-09-14 21:02:26', '1891 年 4 月 7 日', '0', '件',
        '<img src=\"https://resource.smartisan.com/resource/4cbe4a14ab225c9466e16f8c8ef4e29e.jpg\" style=\"width:1220px;height:4083px;\" alt=\"\" />',
        '0', '0', '0', '2022-09-14 21:02:26', '2023-04-17 13:33:47', '0'),
       ('1647788115844136960', '1477055818388660224', '1477055850177290224', 'Smartisan 周边新品', '100033890222',
        'https://resource.smartisan.com/resource/z/zhoubianshangpin1220858web.jpg',
        'https://resource.smartisan.com/resource/406eddef8808fa5a9be9594d07ef8643.jpg,https://resource.smartisan.com/resource/548de41c48d47232b4ed541c1df57c2f.jpg,https://resource.smartisan.com/resource/aee0949bc33654bc18cedb8cd7dfbcff.jpg',
        '199.00', NULL, '2022-09-14 21:02:26', '2022-09-14 21:02:26', '周边新品，应季上新', '0', '件',
        '<img src=\"https://resource.smartisan.com/resource/f6de19257228641b1a0c1964239b19b7.jpg\" style=\"width:1220px;height:9970px;\" alt=\"\" />',
        '0', '0', '0', '2022-09-14 21:02:26', '2023-04-17 13:33:47', '0'),
       ('1647788969091399680', '1477055818388660224', '1477055850177290224', 'Smartisan 帆布鞋', '100033890223',
        'https://resource.smartisan.com/resource/2f9a0f5f3dedf0ed813622003f1b287b.jpg',
        'https://resource.smartisan.com/resource/2f9a0f5f3dedf0ed813622003f1b287b.jpg,https://resource.smartisan.com/resource/0cd8f107c70d002caf902745355e269a.jpg,https://resource.smartisan.com/resource/fa42dcd439e9fb990831f1d21c3f19b8.jpg',
        '199.00', NULL, '2022-09-14 21:02:26', '2022-09-14 21:02:26', '一双踏实、舒适的帆布鞋', '0', '件',
        '<img src=\"https://resource.smartisan.com/resource/27a054301d8e10c40461443928dccd77.jpg\" style=\"width:1220px;height:7451px;\" alt=\"\" />',
        '0', '0', '0', '2022-09-14 21:02:26', '2023-04-17 13:33:47', '0'),
       ('1647790006267281408', '1477055818388660224', '1477055850177290224', 'Smartisan T恤 伍迪·艾伦出生',
        '100033890224', 'https://resource.smartisan.com/resource/2b05dbca9f5a4d0c1270123f42fb834c.jpg',
        'https://resource.smartisan.com/resource/f96f0879768bc317b74e7cf9e3d96884.jpg,https://resource.smartisan.com/resource/095b46c25f9df5b13ee51f3e512d1e96.jpg,https://resource.smartisan.com/resource/0c9c397c8ac68a2ad327e1da8a5cb7d0.jpg,https://resource.smartisan.com/resource/154b35897ed3c1cb8dc1c7aae7b88f1f.jpg,https://resource.smartisan.com/resource/4a1686f2fde86e0aaac49c92395d4b32.jpg',
        '149.00', NULL, '2022-09-14 21:02:26', '2022-09-14 21:02:26', '一件内外兼修的舒适T恤', '0', '件',
        '<img src=\"https://resource.smartisan.com/resource/36fee45879f4f252492ec552dfd4c323.jpg\" style=\"width:1220px;height:6982px;\" alt=\"\" />',
        '0', '0', '0', '2022-09-14 21:02:26', '2023-04-17 13:33:47', '0'),
       ('1647790707097731072', '1477055818388660224', '1477055850177290224', 'Smartisan T恤 任天堂发售 \"红白机\"',
        '100033890225', 'https://resource.smartisan.com/resource/804edf579887b3e1fae4e20a379be5b5.png',
        'https://resource.smartisan.com/resource/804edf579887b3e1fae4e20a379be5b5.png,https://resource.smartisan.com/resource/6a92fe5ab90b379d5315c0ee2610f467.png,https://resource.smartisan.com/resource/76c811504b910e04c448dda8d47a09c3.png',
        '149.00', NULL, '2022-09-14 21:02:26', '2022-09-14 21:02:26', '100% 美国 SUPIMA 棉、舒适拉绒质地', '0', '件',
        '<img src=\"https://resource.smartisan.com/resource/65e89427674ee370fa58f5fe98120679.png\" style=\"width:1220px;height:7881px;\" alt=\"\" />',
        '0', '0', '0', '2022-09-14 21:02:26', '2023-04-17 13:33:47', '0'),
       ('1647791225895387136', '1477055818388660224', '1477055850177290224', 'Smartisan 牛津纺衬衫', '100033890226',
        'https://resource.smartisan.com/resource/a1c53b5f12dd7ef790cadec0eaeaf466.jpg',
        'https://resource.smartisan.com/resource/a1c53b5f12dd7ef790cadec0eaeaf466.jpg,https://resource.smartisan.com/resource/dccec50aa1480c23a6d62648d2271c0a.jpg,https://resource.smartisan.com/resource/28c798c14b3cc9cfe7100567df6e5999.jpg,https://resource.smartisan.com/resource/da87105789046c13412f6f6a32032df7.jpg,https://resource.smartisan.com/resource/cf9704df83dc6d6ff5404da154388a58.jpg',
        '199.00', NULL, '2022-09-14 21:02:26', '2022-09-14 21:02:26', '一件无拘无束的舒适衬衫', '0', '件',
        '<img src=\"https://resource.smartisan.com/resource/debb893778547fb9d5a6119b376d9ec1.jpg\" style=\"width:1220px;height:6879px;\" alt=\"\" />',
        '0', '0', '0', '2022-09-14 21:02:26', '2023-04-17 13:33:47', '0'),
       ('1647791976487059456', '1477055818388660224', '1477055850177290224', 'Smartisan Polo衫 经典款', '100033890227',
        'https://resource.smartisan.com/resource/daa975651d6d700c0f886718c520ee19.jpg',
        'https://resource.smartisan.com/resource/daa975651d6d700c0f886718c520ee19.jpg,https://resource.smartisan.com/resource/8b4884f04835f8de3c33817732fdcb46.jpg,https://resource.smartisan.com/resource/057f6010d6cb7afc964f812093742283.jpg,https://resource.smartisan.com/resource/3cc4b5a1e0a6136eb9725a88d6c1d3be.jpg,https://resource.smartisan.com/resource/f35c053b87dd0e1255be2a8bfa773232.jpg',
        '249.00', NULL, '2022-09-14 21:02:26', '2022-09-14 21:02:26', '一件表里如一的舒适 POLO 衫', '0', '件',
        '<img src=\"https://resource.smartisan.com/resource/41338ebac06fc82450f8b8e4867df257.jpg\" style=\"width:1220px;height:5043px;\" alt=\"\" />',
        '0', '0', '0', '2022-09-14 21:02:26', '2023-04-17 13:33:47', '0'),
       ('1647792528780427264', '1477055818388660224', '1477055850177290224', 'Smartisan 明信片', '100033890228',
        'https://resource.smartisan.com/resource/3973d009d182d8023bea6250b9a3959e.jpg',
        'https://resource.smartisan.com/resource/3973d009d182d8023bea6250b9a3959e.jpg,https://resource.smartisan.com/resource/1901bf9f58d83978353cf1ec58442cc6.jpg,https://resource.smartisan.com/resource/4e0b690102858fc3013ea650fb1e1a8e.jpg,https://resource.smartisan.com/resource/51765f60367d6e40e4ae6f2b9ce46a91.jpg,https://resource.smartisan.com/resource/5108b5e7448c14e5241b60ba41fafc8e.jpg',
        '9.90', NULL, '2022-09-14 21:02:26', '2022-09-14 21:02:26', '优质卡纸、包装精致、色彩饱满', '0', '件',
        '<img src=\"https://resource.smartisan.com/resource/f03a523847e63f28f9238aad45567b37.jpg\" style=\"width:1220px;height:7935px;\" alt=\"\" />',
        '0', '0', '0', '2022-09-14 21:02:26', '2023-04-17 13:33:47', '0'),
       ('1647794693754322944', '1477055818388660224', '1477055850177290224', 'ACIL E1 颈挂失蓝牙耳机', '100033890229',
        'https://resource.smartisan.com/resource/a/acillouceng1220856.jpg',
        'https://resource.smartisan.com/resource/406eddef8808fa5a9be9594d07ef8643.jpg,https://resource.smartisan.com/resource/548de41c48d47232b4ed541c1df57c2f.jpg,https://resource.smartisan.com/resource/aee0949bc33654bc18cedb8cd7dfbcff.jpg',
        '199.00', NULL, '2022-09-14 21:02:26', '2022-09-14 21:02:26', '无感佩戴，一切变得简单', '0', '件',
        '<img src=\"https://resource.smartisan.com/resource/f6de19257228641b1a0c1964239b19b7.jpg\" style=\"width:1220px;height:9970px;\" alt=\"\" />',
        '0', '0', '0', '2022-09-14 21:02:26', '2023-04-17 13:33:47', '0'),
       ('1647795373797801984', '1477055818388660224', '1477055850177290224', '优点智能 E1 推拉式智能指纹密码锁',
        '100033890230', 'https://resource.smartisan.com/resource/7ac3af5a92ad791c2b38bfe1e38ee334.jpg',
        'https://resource.smartisan.com/resource/7ac3af5a92ad791c2b38bfe1e38ee334.jpg,https://resource.smartisan.com/resource/e37029b8cd3194ad9581de0ee6512acb.jpg,https://resource.smartisan.com/resource/1501eaf68c9771e5599eec45a5f6320a.jpg,https://resource.smartisan.com/resource/a8c6a41637041c576aaa2a5162d2ab56.jpg,https://resource.smartisan.com/resource/3934e0c458922c0049d311b84ddb73e0.jpg',
        '2699.00', NULL, '2022-09-14 21:02:26', '2022-09-14 21:02:26', '推拉一下，轻松开关', '0', '件',
        '<img src=\"https://resource.smartisan.com/resource/a1f3fbf662376e8684e528f05721b286.jpg\" style=\"width:1220px;height:14998px;\" alt=\"\" />',
        '0', '0', '0', '2022-09-14 21:02:26', '2023-04-17 13:33:47', '0'),
       ('1647796110271447040', '1477055818388660224', '1477055850177290224', '极米无屏电视 CC', '100033890231',
        'https://resource.smartisan.com/resource/41cb562a47d4831e199ed7e2057f3b61.jpg',
        'http://image.smartisanos.cn/resource/41cb562a47d4831e199ed7e2057f3b61.jpg', '2799.00', NULL,
        '2022-09-14 21:02:26', '2022-09-14 21:02:26', '720P 高清分辨率、JBL 音响、两万毫安续航力', '0', '件',
        '<img src=\"http://image.smartisanos.cn/resource/e7ed8222dcd7c9f67af3097bd7bd5c2b.jpg\" style=\"width:1220px;height:12257px;\" alt=\"\" />',
        '0', '0', '0', '2022-09-14 21:02:26', '2023-04-17 13:33:47', '0'),
       ('1647797092950736896', '1477055818388660224', '1477055850177290224', 'FIIL Diva Pro 全场景无线降噪耳机',
        '100033890232', 'https://resource.smartisan.com/resource/99c548bfc9848a8c95f4e4f7f2bc1095.png',
        'https://resource.smartisan.com/resource/99c548bfc9848a8c95f4e4f7f2bc1095.png', '1499.00', NULL,
        '2022-09-14 21:02:26', '2022-09-14 21:02:26', '智能语音交互、高清无损本地存储播放', '0', '件',
        '<img src=\"http://image.smartisanos.cn/resource/9be6229b3498749c4afd173a3b1fe165.png\" style=\"width:1220px;height:15514px;\" alt=\"\" />',
        '0', '0', '0', '2022-09-14 21:02:26', '2023-04-17 13:33:47', '0'),
       ('1647797850492370944', '1477055818388660224', '1477055850177290224', '畅呼吸智能空气净化器超级除甲醛版',
        '100033890233', 'https://resource.smartisan.com/resource/71432ad30288fb860a4389881069b874.png',
        'https://resource.smartisan.com/resource/71432ad30288fb860a4389881069b874.png', '2999.00', NULL,
        '2022-09-14 21:02:26', '2022-09-14 21:02:26', '购新空净 赠价值 699 元活性炭滤芯', '0', '件',
        '<img src=\"https://img20.360buyimg.com/vc/jfs/t11503/47/1494539812/1725396/3defedb6/5a046e18Ne0a5d5da.jpg\" alt=\"\" />',
        '0', '0', '0', '2022-09-14 21:02:26', '2023-04-17 13:33:47', '0'),
       ('1647798317083525120', '1477055818388660224', '1477055850177290224', 'FIIL Diva Pro 全场景无线降噪耳机',
        '100033890234', 'https://resource.smartisan.com/resource/804b82e4c05516e822667a23ee311f7c.jpg',
        'http://image.smartisanos.cn/resource/804b82e4c05516e822667a23ee311f7c.jpg', '1499.00', NULL,
        '2022-09-14 21:02:26', '2022-09-14 21:02:26', '智能语音交互、高清无损本地存储播放', '0', '件',
        '<img src=\"http://image.smartisanos.cn/resource/9be6229b3498749c4afd173a3b1fe165.png\" style=\"width:1220px;height:15514px;\" alt=\"\" />',
        '0', '0', '0', '2022-09-14 21:02:26', '2023-04-17 13:33:47', '0'),
       ('1647799053083213824', '1477055818388660224', '1477055850177290224', 'FIIL Driifter 脖挂蓝牙耳机',
        '100033890235', 'https://resource.smartisan.com/resource/367d93db1d58f9f3505bc0ec18efaa44.jpg',
        'https://resource.smartisan.com/resource/367d93db1d58f9f3505bc0ec18efaa44.jpg,https://resource.smartisan.com/resource/8ecc94c0f0c4ebc861f06c86789a66e6.jpg,https://resource.smartisan.com/resource/58a2cdb44f722202b05dd09d6fd959de.jpg,https://resource.smartisan.com/resource/2b811a93a2915310f72291e46bd944ad.jpg,https://resource.smartisan.com/resource/8cd011adef99f9734ed974ea9732e6e7.jpg',
        '499.00', NULL, '2022-09-14 21:02:26', '2022-09-14 21:02:26', '全天佩戴 抬手就听 HEAC稳连技术', '0', '件',
        '<img src=\"https://resource.smartisan.com/resource/4bcf112ee0b7fd0391d5980e41b687b6.jpg\" style=\"width:1220px;height:12130px;\" />',
        '0', '0', '0', '2022-09-14 21:02:26', '2023-04-17 13:33:47', '0');