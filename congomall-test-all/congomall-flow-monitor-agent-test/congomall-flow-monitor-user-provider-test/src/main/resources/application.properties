

server.port=19010
spring.profiles.active=dev
spring.application.name=flow-monitor-user-provider-test

spring.cloud.nacos.discovery.server-addr=localhost:8848

management.metrics.export.prometheus.enabled=true
management.endpoints.web.exposure.include=*

xxl.job.admin.addresses=http://localhost:8080/xxl-job-admin
xxl.job.executor.appname=xxl-job-executor-sample2
xxl.job.executor.logpath=/Users/<USER>/data/xxljob
xxl.job.executor.logretentiondays=30
xxl.job.executor.port=19998
xxl.job.executor.ip=**************
# xxl.job.executor.ip=***********
xxl.job.accessToken=default_token

congomall.swagger.title=User Provide Test
congomall.swagger.version=1.0.0
congomall.swagger.description=User Provide Test
congomall.swagger.contact.name=chen.ma
