<?xml version="1.0" encoding="UTF-8"?>


<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.opengoofy.congomall</groupId>
        <artifactId>congomall-test-all</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <artifactId>congomall-flow-monitor-agent-test</artifactId>
    <packaging>pom</packaging>
    
    <modules>
        <module>congomall-flow-monitor-message-provider-test</module>
        <module>congomall-flow-monitor-order-consumer-test</module>
        <module>congomall-flow-monitor-pay-consumer-test</module>
        <module>congomall-flow-monitor-trading-consumer-test</module>
        <module>congomall-flow-monitor-user-provider-test</module>
    </modules>
    
    <build>
        <finalName>flow-monitor-message-provider-test</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
