

server.port=19000
spring.profiles.active=dev
spring.application.name=flow-monitor-message-provider-test

spring.cloud.nacos.discovery.server-addr=localhost:8848

management.metrics.export.prometheus.enabled=true
management.endpoints.web.exposure.include=*

congomall.swagger.title=User Provide Test
congomall.swagger.version=1.0.0
congomall.swagger.description=User Provide Test
congomall.swagger.contact.name=chen.ma

xxl.job.admin.addresses=http://localhost:8080/xxl-job-admin
xxl.job.executor.appname=xxl-job-executor-sample
xxl.job.executor.logpath=/Users/<USER>/data/xxljob
xxl.job.executor.logretentiondays=30
xxl.job.executor.port=19999
xxl.job.executor.ip=*************
# xxl.job.executor.ip=***********
xxl.job.accessToken=default_token

# rocketmq nameserver \u5730\u5740
spring.cloud.stream.rocketmq.binder.name-server=127.0.0.1:9876

# spring cloud stream output
spring.cloud.stream.bindings.output.content-type=application/json
spring.cloud.stream.bindings.output.destination=flow-monitor_message_provider_test_topic
spring.cloud.stream.bindings.output.group=flow-monitor_message_provider_test_pg

# spring cloud stream input
spring.cloud.stream.bindings.input.content-type=application/json
spring.cloud.stream.bindings.input.destination=flow-monitor_message_provider_test_topic
spring.cloud.stream.bindings.input.group=flow-monitor_message_provider_test_cg
spring.cloud.stream.bindings.input.consumer.max-attempts=1
spring.cloud.stream.bindings.input.consumer.concurrency=4
