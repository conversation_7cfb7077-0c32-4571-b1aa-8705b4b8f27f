<?xml version="1.0" encoding="UTF-8"?>


<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.opengoofy.congomall</groupId>
        <artifactId>congomall-flow-monitor-agent-test</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <artifactId>congomall-flow-monitor-message-provider-test</artifactId>
    
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
    
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
            <version>1.5.3</version>
        </dependency>
        
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>${xxl-job.version}</version>
        </dependency>
        
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-stream-rocketmq</artifactId>
        </dependency>
        
        <dependency>
            <groupId>org.opengoofy.congomall</groupId>
            <artifactId>congomall-rocketmq-spring-boot-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        
        <dependency>
            <groupId>org.opengoofy.congomall</groupId>
            <artifactId>congomall-swagger-spring-boot-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
    
        <dependency>
            <groupId>org.opengoofy.congomall</groupId>
            <artifactId>congomall-convention-spring-boot-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>
    
    <build>
        <finalName>flow-monitor-message-provider-test</finalName>
    </build>
</project>
