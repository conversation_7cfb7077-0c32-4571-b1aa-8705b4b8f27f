

server.port=19002
spring.profiles.active=dev
spring.application.name=flow-monitor-pay-consumer-test

ribbon.MaxAutoRetries=0
ribbon.MaxAutoRetriesNextServer=0
ribbon.okToRetryOnAllOperations=false
ribbon.ConnectTimeout=200000
ribbon.ReadTimeout=200000

hystrix.command.default.execution.timeout.enabled=false
hystrix.command.default.execution.isolation.thread.timeoutInMilliseconds=600000

spring.cloud.nacos.discovery.server-addr=localhost:8848

congomall.swagger.title=Pay Consumer Test
congomall.swagger.version=1.0.0
congomall.swagger.description=Pay Consumer Test
congomall.swagger.contact.name=chen.ma
