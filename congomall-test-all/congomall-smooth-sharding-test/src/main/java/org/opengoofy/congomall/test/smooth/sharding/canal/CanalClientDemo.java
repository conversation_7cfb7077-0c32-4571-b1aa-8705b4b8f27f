

package org.opengoofy.congomall.test.smooth.sharding.canal;

//
// import com.alibaba.otter.canal.client.CanalConnector;
// import com.alibaba.otter.canal.client.CanalConnectors;
// import com.alibaba.otter.canal.common.utils.AddressUtils;
// import com.alibaba.otter.canal.protocol.CanalEntry.*;
// import com.alibaba.otter.canal.protocol.Message;
// import lombok.extern.slf4j.Slf4j;
// import org.springframework.boot.CommandLineRunner;
// import org.springframework.stereotype.Component;
//
// import java.net.InetSocketAddress;
// import java.util.List;
//
/// **
// * Canal 客户端测试
// *
// * <AUTHOR>
// * @github <a href="https://github.com/opengoofy" />
// * @公众号  JavaEdge，关注回复：架构师，领取后端架构师成长手册
// */
// @Slf4j
// @Component
// public class CanalClientDemo implements CommandLineRunner {
//
// @Override
// public void run(String... args) throws Exception {
// CanalConnector connector = CanalConnectors.newSingleConnector(new InetSocketAddress(AddressUtils.getHostIp(),
// 11111), "example", "", "");
// int batchSize = 1000;
// int emptyCount = 0;
// try {
// connector.connect();
// connector.subscribe("canal_test.pay_info");
// int totalEmptyCount = 120;
// while (emptyCount < totalEmptyCount) {
// Message message = connector.getWithoutAck(batchSize); // 获取指定数量的数据
// long batchId = message.getId();
// int size = message.getEntries().size();
// if (batchId == -1 || size == 0) {
// emptyCount++;
// System.out.println("empty count: " + emptyCount);
// try {
// Thread.sleep(1000);
// } catch (InterruptedException e) {
// }
// } else {
// emptyCount = 0;
// printEntry(message.getEntries());
// }
// connector.ack(batchId);
// }
// System.out.println("empty too many times, exit");
// } finally {
// connector.disconnect();
// }
// }
//
// private static void printEntry(List<Entry> entrys) {
// for (Entry entry : entrys) {
// if (entry.getEntryType() == EntryType.TRANSACTIONBEGIN || entry.getEntryType() == EntryType.TRANSACTIONEND) {
// continue;
// }
// RowChange rowChange;
// try {
// rowChange = RowChange.parseFrom(entry.getStoreValue());
// } catch (Exception e) {
// throw new RuntimeException("ERROR ## parser of error change-event has an error, data: " + entry, e);
// }
// EventType eventType = rowChange.getEventType();
// System.out.println(String.format("================> binlog[%s:%s], name[%s,%s], eventType: %s",
// entry.getHeader().getLogfileName(), entry.getHeader().getLogfileOffset(),
// entry.getHeader().getSchemaName(), entry.getHeader().getTableName(),
// eventType));
// for (RowData rowData : rowChange.getRowDatasList()) {
// if (eventType == EventType.DELETE) {
// printColumn(rowData.getBeforeColumnsList());
// } else if (eventType == EventType.INSERT) {
// printColumn(rowData.getAfterColumnsList());
// } else {
// System.out.println("-------> before");
// printColumn(rowData.getBeforeColumnsList());
// System.out.println("-------> after");
// printColumn(rowData.getAfterColumnsList());
// }
// }
// }
// }
//
// private static void printColumn(List<Column> columns) {
// for (Column column : columns) {
// System.out.println(column.getName() + ": " + column.getValue() + " update=" + column.getUpdated());
// }
// }
// }
