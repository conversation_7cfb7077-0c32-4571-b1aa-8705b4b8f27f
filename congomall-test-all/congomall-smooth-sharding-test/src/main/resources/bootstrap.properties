server.port=16000
spring.profiles.active=dev
# \u5E94\u7528\u540D\u79F0
spring.application.name=pay-service

# Swagger
congomall.swagger.title=Smooth Sharding Service
congomall.swagger.version=1.0.0
congomall.swagger.description=Smooth Sharding Service
congomall.swagger.contact.name=chen.ma

spring.cloud.nacos.config.server-addr=localhost:8848
spring.cloud.nacos.config.username=nacos
spring.cloud.nacos.config.password=nacos
spring.cloud.nacos.config.extension-configs[0].data-id=pay-service.properties
spring.cloud.nacos.config.extension-configs[0].group=DEFAULT_GROUP
spring.cloud.nacos.config.extension-configs[0].refresh=true

spring.shardingsphere.datasource.ds-0.type=com.zaxxer.hikari.HikariDataSource
spring.shardingsphere.datasource.ds-0.driver-class-name=com.mysql.jdbc.Driver
spring.shardingsphere.datasource.ds-0.jdbc-url=****************************************************************************************************************************************************************************************************
spring.shardingsphere.datasource.ds-0.username=root
spring.shardingsphere.datasource.ds-0.password=root

# åçæ°æ®åºå«å
spring.shardingsphere.datasource.names=ds-0
# æ¯å¦æå° ShardingSphere SQL è¯­å¥
spring.shardingsphere.props.sql-show=true
# åçç©çè¡¨çå®èç¹
spring.shardingsphere.rules.sharding.tables.pay_info.actual-data-nodes=ds-0.pay_info_shard_$->{2012..2025},ds-0.pay_info
# åçç®æ³åç§°
spring.shardingsphere.rules.sharding.tables.pay_info.table-strategy.standard.sharding-algorithm-name=sharding_by_time
# åçç­ç¥ä¹åé®å®ä¹
spring.shardingsphere.rules.sharding.tables.pay_info.table-strategy.standard.sharding-column=create_time
# åçç®æ³ç­ç¥ç±»å
spring.shardingsphere.rules.sharding.sharding-algorithms.sharding_by_time.props.strategy=standard
# åçç®æ³å®ç°ç±»å
spring.shardingsphere.rules.sharding.sharding-algorithms.sharding_by_time.type=CLASS_BASED
# åçç®æ³å®ç°ç±»
spring.shardingsphere.rules.sharding.sharding-algorithms.sharding_by_time.props.algorithmClassName=org.opengoofy.congomall.test.smooth.sharding.shard.PayCreateTimeStandardShardingAlgorithm

# Mybatis SQL æå°ç±»
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
