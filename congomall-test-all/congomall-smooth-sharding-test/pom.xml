<?xml version="1.0" encoding="UTF-8"?>


<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.opengoofy.congomall</groupId>
        <artifactId>congomall-test-all</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <artifactId>congomall-smooth-sharding-test</artifactId>
    
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        
        <dependency>
            <groupId>com.alibaba.otter</groupId>
            <artifactId>canal.client</artifactId>
            <version>${canal-client.version}</version>
        </dependency>
        
        <dependency>
            <groupId>com.alibaba.otter</groupId>
            <artifactId>canal.protocol</artifactId>
            <version>${canal-client.version}</version>
        </dependency>
        
        <dependency>
            <groupId>org.opengoofy.congomall</groupId>
            <artifactId>congomall-base-spring-boot-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <dependency>
            <groupId>org.opengoofy.congomall</groupId>
            <artifactId>congomall-database-spring-boot-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <dependency>
            <groupId>org.apache.shardingsphere</groupId>
            <artifactId>shardingsphere-jdbc-core-spring-boot-starter</artifactId>
        </dependency>
        
        <dependency>
            <groupId>org.opengoofy.congomall</groupId>
            <artifactId>congomall-swagger-spring-boot-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-context</artifactId>
        </dependency>
    </dependencies>
</project>
