

congomall:
  fastjson:
    safa-mode: true
  cache:
    redis:
      prefix: ''
      value-timeout: 50000
  swagger:
    contact:
      name: chen.ma
    description: Customer User Service
    title: Customer User Service
    version: 1.0.0

customer:
  user:
    register:
      verify:
        sender: <EMAIL>
        template-id: userRegisterVerification

feign:
  client:
    config:
      congomall-message:
        connect-timeout: 2000
        read-timeout: 3000

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0

server:
  port: 8002

spring:
  application:
    name: congomall-customer-user
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
      password: nacos
      username: nacos
    sentinel:
      transport:
        dashboard: localhost:8686
        port: 8719
    stream:
      bindings:
        input:
          consumer:
            concurrency: 4
            max-attempts: 1
          content-type: application/json
          destination: customer-user_topic
          group: customer-user_operation-log_cg
        output:
          content-type: application/json
          destination: customer-user_topic
          group: customer-user_general-send_pg
      rocketmq:
        binder:
          name-server: 127.0.0.1:9876
        bindings:
          input:
            consumer:
              delay-level-when-next-consume: -1
              tags: customer-user_operation-log_tag
  profiles:
    active: dev
  redis:
    host: localhost
    # password: 123456
    port: 6379
  shardingsphere:
    datasource:
      ds-0:
        driver-class-name: com.mysql.jdbc.Driver
        jdbc-url: ************************************************************************************************************************************************************************************************************
        username: root
        password: 123456
        type: com.zaxxer.hikari.HikariDataSource
      names: ds-0
    mode:
      type: Memory
    props:
      sql-show: true
    rules:
      encrypt:
        encryptors:
          customer-user-encryptor:
            props:
              aes-key-value: ADbisulBtxnnKFoW
            type: AES
        tables:
          customer_user:
            columns:
              mail:
                cipher-column: mail
                encryptor-name: customer-user-encryptor
              phone:
                cipher-column: phone
                encryptor-name: customer-user-encryptor
      sharding:
        sharding-algorithms:
          sharding_by_mod:
            props:
              sharding-count: 4
            type: HASH_MOD
        tables:
          customer_user:
            actual-data-nodes: ds-0.customer_user_$->{0..3}
            table-strategy:
              standard:
                sharding-algorithm-name: sharding_by_mod
                sharding-column: id
    schema:
      name: congomall_customer_user_manager
