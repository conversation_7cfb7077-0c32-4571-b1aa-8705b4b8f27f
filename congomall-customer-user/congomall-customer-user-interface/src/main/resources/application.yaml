

server:
  port: 8002
  servlet:
    context-path: 

spring:
  profiles:
    active: dev
  application:
    name: customer-user-service
  cloud:
    sentinel:
      transport:
        dashboard: localhost:8686
        port: 8719
    stream:
      bindings:
        userInput:
          consumer:
            concurrency: 4
            max-attempts: 1
          content-type: application/json
          destination: customer-user_topic
          group: customer-user_operation-log_cg
        userOutput:
          content-type: application/json
          destination: customer-user_topic
          group: customer-user_general-send_pg
      rocketmq:
        bindings:
          userInput:
            consumer:
              delay-level-when-next-consume: -1
              tags: customer-user_operation-log_tag
  shardingsphere:
    datasource:
      ds-0:
        driver-class-name: com.mysql.jdbc.Driver
        type: com.zaxxer.hikari.HikariDataSource
      names: ds-0
    props:
      sql-show: true
      max-connections-size-per-query: 10
    rules:
      encrypt:
        encryptors:
          customer-user-encryptor:
            props:
              aes-key-value: ADbisulBtxnnKFoW
            type: AES
        tables:
          customer_user:
            columns:
              mail:
                cipher-column: mail
                encryptor-name: customer-user-encryptor
              phone:
                cipher-column: phone
                encryptor-name: customer-user-encryptor
          receive_address:
            columns:
              phone:
                cipher-column: phone
                encryptor-name: customer-user-encryptor
              detail_address:
                cipher-column: detail_address
                encryptor-name: customer-user-encryptor
      sharding:
        sharding-algorithms:
          sharding_by_mod:
            props:
              sharding-count: 16
            type: HASH_MOD
        tables:
          customer_user:
            actual-data-nodes: ds-0.customer_user_$->{0..15}
            table-strategy:
              standard:
                sharding-algorithm-name: sharding_by_mod
                sharding-column: id
          operation_log:
            actual-data-nodes: ds-0.operation_log_$->{0..15}
            table-strategy:
              standard:
                sharding-algorithm-name: sharding_by_mod
                sharding-column: customer_user_id
          receive_address:
            actual-data-nodes: ds-0.receive_address_$->{0..15}
            table-strategy:
              standard:
                sharding-algorithm-name: sharding_by_mod
                sharding-column: customer_user_id

congomall:
  fastjson:
    safa-mode: true
  swagger:
    contact:
      name: chen.ma
    description: Customer User Service
    title: Customer User Service
    version: 1.0.0
  cache:
    redis:
      prefix: ''
      value-timeout: 50000

customer:
  user:
    register:
      verify:
        sender: <EMAIL>
        template-id: userRegisterVerification

feign:
  client:
    config:
      default:
        connectTimeout: 5000
        loggerLevel: HEADERS
        readTimeout: 5000
  httpclient:
    enabled: false
  okhttp:
    enabled: true

mybatis-plus:
  global-config:
    db-config:
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0

ribbon:
  eager-load:
    clients: message-service
    enabled: true
