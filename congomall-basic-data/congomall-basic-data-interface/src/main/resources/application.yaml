

server:
  port: 8008

spring:
  profiles:
    active: dev
  application:
    name: basics-data-service
  cloud:
    sentinel:
      transport:
        dashboard: localhost:8686
        port: 8719
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      connection-test-query: select 1
      connection-timeout: 20000
      idle-timeout: 300000
      maximum-pool-size: 5
      minimum-idle: 5

congomall:
  fastjson:
    safa-mode: true
  swagger:
    contact:
      name: chen.ma
    description: Basics Data Service
    title: Basics Data Service
    version: 1.0.0

mybatis-plus:
  global-config:
    db-config:
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0
