<?xml version="1.0" encoding="UTF-8"?>


<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.opengoofy.congomall</groupId>
        <artifactId>congomall-order</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <artifactId>congomall-order-application</artifactId>
    
    <dependencies>
        <dependency>
            <groupId>org.opengoofy.congomall</groupId>
            <artifactId>congomall-order-infrastructure</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <dependency>
            <groupId>org.opengoofy.congomall</groupId>
            <artifactId>congomall-ddd-framework-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <dependency>
            <groupId>org.opengoofy.congomall</groupId>
            <artifactId>congomall-swagger-spring-boot-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <dependency>
            <groupId>org.opengoofy.congomall</groupId>
            <artifactId>congomall-log-spring-boot-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <dependency>
            <groupId>org.opengoofy.congomall</groupId>
            <artifactId>congomall-distributedid-spring-boot-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
    
        <dependency>
            <groupId>org.opengoofy.congomall</groupId>
            <artifactId>congomall-designpattern-spring-boot-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>
</project>
