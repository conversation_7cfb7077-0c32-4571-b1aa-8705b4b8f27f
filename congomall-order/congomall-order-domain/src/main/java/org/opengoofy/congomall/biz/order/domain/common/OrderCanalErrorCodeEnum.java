

package org.opengoofy.congomall.biz.order.domain.common;

import lombok.AllArgsConstructor;
import org.opengoofy.congomall.springboot.starter.convention.errorcode.IErrorCode;

/**
 * 订单取消错误码枚举
 *
 * <AUTHOR>
 * @github <a href="https://github.com/Java-Edge" />
 * @公众号 JavaEdge，关注回复：架构师，领取后端架构师成长手册
 */
@AllArgsConstructor
public enum OrderCanalErrorCodeEnum implements IErrorCode {
    
    ORDER_CANAL_UNKNOWN_ERROR("B006001", "订单不存在，请检查相关订单记录"),
    
    ORDER_CANAL_STATUS_ERROR("B006002", "订单状态不正确，请检查相关订单记录"),
    
    ORDER_CANAL_ERROR("B006003", "订单取消失败，请稍后再试"),
    
    ORDER_CANAL_REPETITION_ERROR("B006004", "订单重复取消，请稍后再试"),
    
    ORDER_STATUS_REVERSAL_ERROR("B006005", "订单状态反转失败，请稍后再试"),
    
    ORDER_DELETE_ERROR("B006006", "订单状态反转失败，请稍后再试");
    
    /**
     * 错误码
     */
    private final String code;
    
    /**
     * 错误提示消息
     */
    private final String message;
    
    @Override
    public String code() {
        return code;
    }
    
    @Override
    public String message() {
        return message;
    }
}
