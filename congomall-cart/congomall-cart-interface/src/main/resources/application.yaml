

server:
  port: 8005

spring:
  profiles:
    active: dev
  application:
    name: cart-service
  cloud:
    sentinel:
      transport:
        dashboard: localhost:8686
        port: 8719
  shardingsphere:
    datasource:
      ds-0:
        driver-class-name: com.mysql.jdbc.Driver
        type: com.zaxxer.hikari.HikariDataSource
      names: ds-0
    props:
      sql-show: true
    rules:
      sharding:
        sharding-algorithms:
          cart-item_sharding_by_mod:
            props:
              sharding-count: 16
            type: HASH_MOD
        tables:
          cart_item:
            actual-data-nodes: ds-0.cart_item_$->{0..15}
            table-strategy:
              standard:
                sharding-algorithm-name: cart-item_sharding_by_mod
                sharding-column: customer_user_id

congomall:
  fastjson:
    safa-mode: true
  swagger:
    contact:
      name: chen.ma
    description: Cart Service
    title: Cart Service
    version: 1.0.0
  cart:
    max-product: 500

mybatis-plus:
  global-config:
    db-config:
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0

seata:
  application-id: ${spring.application.name}
  enable-auto-data-source-proxy: false
  service:
    grouplist:
      seata-server: 127.0.0.1:8091
    vgroup-mapping:
      my-tx-group: seata-server
  tx-service-group: my-tx-group
  use-jdk-proxy: true