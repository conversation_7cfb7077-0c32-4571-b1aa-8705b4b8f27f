<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.opengoofy.congomall</groupId>
        <artifactId>congomall-framework-all</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <artifactId>congomall-idempotent-spring-boot-starter</artifactId>
    
    <dependencies>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
        </dependency>
        
        <dependency>
            <groupId>org.opengoofy.congomall</groupId>
            <artifactId>congomall-base-spring-boot-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <dependency>
            <groupId>org.opengoofy.congomall</groupId>
            <artifactId>congomall-cache-spring-boot-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <dependency>
            <groupId>org.opengoofy.congomall</groupId>
            <artifactId>congomall-web-spring-boot-starter</artifactId>
            <version>${project.version}</version>
            <optional>true</optional>
        </dependency>
        
        <dependency>
            <groupId>org.opengoofy.congomall</groupId>
            <artifactId>congomall-convention-spring-boot-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
        </dependency>
        
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>
</project>
