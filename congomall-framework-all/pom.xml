<?xml version="1.0" encoding="UTF-8"?>


<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.opengoofy.congomall</groupId>
        <artifactId>congomall-all</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <artifactId>congomall-framework-all</artifactId>
    <packaging>pom</packaging>
    
    <modules>
        <!-- 基础架构组件库 -->
        <module>congomall-base-spring-boot-starter</module>
        <module>congomall-cache-spring-boot-starter</module>
        <module>congomall-common-spring-boot-starter</module>
        <module>congomall-convention-spring-boot-starter</module>
        <module>congomall-designpattern-spring-boot-starter</module>
        <module>congomall-distributedid-spring-boot-starter</module>
        <module>congomall-httputil-spring-boot-starter</module>
        <module>congomall-idempotent-spring-boot-starter</module>
        <module>congomall-log-spring-boot-starter</module>
        <module>congomall-minio-spring-boot-starter</module>
        <module>congomall-database-spring-boot-starter</module>
        <module>congomall-openfeign-spring-boot-starter</module>
        <module>congomall-rocketmq-spring-boot-starter</module>
        <module>congomall-sensitive-spring-boot-starter</module>
        <module>congomall-swagger-spring-boot-starter</module>
        <module>congomall-web-spring-boot-starter</module>
        <module>congomall-xxljob-spring-boot-starter</module>
        <!-- 微服务流量监控 -->
        <module>congomall-flow-monitor-agent</module>
        <!-- DDD 核心组件 -->
        <module>congomall-ddd-framework-core</module>
    </modules>
</project>
