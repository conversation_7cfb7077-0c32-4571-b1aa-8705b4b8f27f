<?xml version="1.0" encoding="UTF-8"?>


<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>org.opengoofy.congomall</groupId>
    <artifactId>congomall-all</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <url>https://github.com/opengoofy</url>
    <description>
        CongoMall（刚果商城）基于 DDD 开发的商城系统，内置分布式锁、分布式事务、分库分表、消息队列、数据搜索、服务监控等功能。
    </description>
    
    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    
    <developers>
        <developer>
            <name>chen.ma</name>
            <email><EMAIL></email>
            <url>https://github.com/magestacks</url>
        </developer>
    </developers>
    
    <modules>
        <module>congomall-aggregation</module>
        <module>congomall-basic-data</module>
        <module>congomall-bff</module>
        <module>congomall-cart</module>
        <module>congomall-customer-user</module>
        <module>congomall-framework-all</module>
        <module>congomall-gateway</module>
        <module>congomall-message</module>
        <module>congomall-order</module>
        <module>congomall-pay</module>
        <module>congomall-product</module>
        <module>congomall-test-all</module>
    </modules>
    
    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spotless-maven-plugin.version>2.22.1</spotless-maven-plugin.version>
        <maven-checkstyle-plugin.version>3.1.0</maven-checkstyle-plugin.version>
        <!-- framework start -->
        <spring-boot.version>2.3.12.RELEASE</spring-boot.version>
        <spring-cloud-dependencies.version>Hoxton.SR12</spring-cloud-dependencies.version>
        <spring-cloud-alibaba-dependencies.version>2.2.9.RELEASE</spring-cloud-alibaba-dependencies.version>
        <spring-cloud-starter-openfeign.version>2.2.5.RELEASE</spring-cloud-starter-openfeign.version>
        <knife4j-spring-boot-starter.version>3.0.3</knife4j-spring-boot-starter.version>
        <redisson-spring-boot-starter.version>3.17.3</redisson-spring-boot-starter.version>
        <shardingsphere-jdbc-core-spring-boot-starter.version>5.2.0
        </shardingsphere-jdbc-core-spring-boot-starter.version>
        <spring-cloud-starter-stream-rocketmq.version>2.2.6.RELEASE</spring-cloud-starter-stream-rocketmq.version>
        <spring-cloud-starter-alibaba-sentinel.version>2.2.7.RELEASE</spring-cloud-starter-alibaba-sentinel.version>
        <minio.verion>8.4.2</minio.verion>
        <hippo4j-config.version>1.4.3</hippo4j-config.version>
        <seata.version>1.5.2</seata.version>
        <xxl-job.version>2.3.1</xxl-job.version>
        <canal-client.version>1.1.6</canal-client.version>
        <apm-toolkit-trace.version>8.14.0</apm-toolkit-trace.version>
        <netty-all.version>4.1.68.Final</netty-all.version>
        <!-- framework end -->
        <!-- tool start -->
        <lombok.version>1.18.24</lombok.version>
        <mapstruct.version>1.4.2.Final</mapstruct.version>
        <dozer.version>6.5.2</dozer.version>
        <fastjson2.version>2.0.7</fastjson2.version>
        <hutool-all.version>5.8.2</hutool-all.version>
        <guava.version>31.1-jre</guava.version>
        <hibernate-validator.version>5.4.2.Final</hibernate-validator.version>
        <validation-api.version>2.0.1.Final</validation-api.version>
        <jjwt.version>0.9.1</jjwt.version>
        <okhttp3.version>4.9.3</okhttp3.version>
        <javafaker.version>1.0.2</javafaker.version>
        <jetcache.version>2.7.3</jetcache.version>
        <!-- tool end -->
    </properties>
    
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                <version>${spring-cloud-alibaba-dependencies.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>spring-cloud-netflix-archaius</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-openfeign</artifactId>
                <version>${spring-cloud-starter-openfeign.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>spring-cloud-netflix-archaius</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-stream-rocketmq</artifactId>
                <version>${spring-cloud-starter-stream-rocketmq.version}</version>
            </dependency>
            
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
                <version>${spring-cloud-starter-alibaba-sentinel.version}</version>
            </dependency>
            
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate-validator.version}</version>
            </dependency>
            
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${validation-api.version}</version>
            </dependency>
            
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool-all.version}</version>
            </dependency>
            
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>
            
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson-spring-boot-starter.version}</version>
            </dependency>
            
            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>shardingsphere-jdbc-core-spring-boot-starter</artifactId>
                <version>${shardingsphere-jdbc-core-spring-boot-starter.version}</version>
            </dependency>
            
            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>shardingsphere-transaction-base-seata-at</artifactId>
                <version>${shardingsphere-jdbc-core-spring-boot-starter.version}</version>
            </dependency>
            
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp3.version}</version>
            </dependency>
            
            <dependency>
                <groupId>com.github.javafaker</groupId>
                <artifactId>javafaker</artifactId>
                <version>${javafaker.version}</version>
            </dependency>
            
            <dependency>
                <groupId>cn.hippo4j</groupId>
                <artifactId>hippo4j-config-spring-boot-starter</artifactId>
                <version>${hippo4j-config.version}</version>
            </dependency>
            
            <dependency>
                <groupId>io.seata</groupId>
                <artifactId>seata-spring-boot-starter</artifactId>
                <version>${seata.version}</version>
            </dependency>
            
            <!-- Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. -->
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>${netty-all.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    
    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>
    </dependencies>
    
    <build>
        <plugins>
            <plugin>
                <groupId>com.diffplug.spotless</groupId>
                <artifactId>spotless-maven-plugin</artifactId>
                <version>${spotless-maven-plugin.version}</version>
                <configuration>
                    <java>
                        <!--<eclipse>
                            <file>${maven.multiModuleProjectDirectory}/dev-support/congomall_spotless_formatter.xml
                            </file>
                        </eclipse>-->
                        <licenseHeader>
                            <file>${maven.multiModuleProjectDirectory}/dev-support/license-header</file>
                        </licenseHeader>
                    </java>
                    <!--<pom>
                        <sortPom>
                            <encoding>UTF-8</encoding>
                            <nrOfIndentSpace>4</nrOfIndentSpace>
                            <keepBlankLines>true</keepBlankLines>
                            <indentBlankLines>true</indentBlankLines>
                            <indentSchemaLocation>true</indentSchemaLocation>
                            <spaceBeforeCloseEmptyElement>true</spaceBeforeCloseEmptyElement>
                            <sortModules>false</sortModules>
                            <sortExecutions>false</sortExecutions>
                            <predefinedSortOrder>custom_1</predefinedSortOrder>
                            <expandEmptyElements>false</expandEmptyElements>
                            <sortProperties>false</sortProperties>
                        </sortPom>
                    </pom>-->
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>apply</goal>
                        </goals>
                        <phase>compile</phase>
                    </execution>
                </executions>
            </plugin>
            
            <plugin>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>${maven-checkstyle-plugin.version}</version>
                <configuration>
                    <configLocation>${maven.multiModuleProjectDirectory}/dev-support/checkstyle.xml</configLocation>
                    <includeTestSourceDirectory>true</includeTestSourceDirectory>
                    <excludes>**/autogen/**/*</excludes>
                </configuration>
                <executions>
                    <execution>
                        <id>validate</id>
                        <goals>
                            <goal>check</goal>
                        </goals>
                        <phase>validate</phase>
                    </execution>
                </executions>
            </plugin>
            
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>
                                build-info
                            </goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
