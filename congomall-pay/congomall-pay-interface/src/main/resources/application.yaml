

server:
  port: 8007

spring:
  profiles:
    active: dev
  application:
    name: pay-service
  cloud:
    sentinel:
      transport:
        dashboard: localhost:8686
        port: 8719
    stream:
      bindings:
        payOutput:
          content-type: application/json
          destination: pay-service_topic
          group: pay-service_general-send_pg
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      connection-test-query: select 1
      connection-timeout: 20000
      idle-timeout: 300000
      maximum-pool-size: 5
      minimum-idle: 5

congomall:
  fastjson:
    safa-mode: true
  swagger:
    contact:
      name: chen.ma
    description: Pay Service
    title: Pay Service
    version: 1.0.0

mybatis-plus:
  global-config:
    db-config:
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath:mapper/*.xml
