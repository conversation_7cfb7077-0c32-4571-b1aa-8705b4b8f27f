# 支付服务操作复现指南

## 前置条件检查

### 1. 检查Java环境
```bash
java -version
```
确保Java 8或以上版本已安装。

### 2. 检查Maven环境
```bash
mvn -version
```
确保Maven已正确安装。

### 3. 检查项目目录
```bash
cd /Users/<USER>/soft/IDEAProjects/ddd-mall
ls -la
```
确保在正确的项目根目录。

## 第一步：启动基础服务

### 1.1 检查MySQL服务状态
```bash
# 检查MySQL是否运行
ps aux | grep mysql | grep -v grep
```

如果MySQL未运行，启动MySQL：
```bash
sudo /usr/local/mysql/support-files/mysql.server start
```

### 1.2 检查Redis服务状态
```bash
# 检查Redis是否运行
ps aux | grep redis | grep -v grep
```

如果Redis未运行，启动Redis：
```bash
redis-server &
```

### 1.3 验证数据库连接
```bash
# 连接MySQL并检查数据库
/usr/local/mysql/bin/mysql -u root -p123456 -e "SHOW DATABASES LIKE 'congomall_pay';"
```

## 第二步：编译和启动支付服务

### 2.1 编译项目
```bash
# 在项目根目录执行
mvn clean compile -DskipTests
```

### 2.2 启动支付服务
```bash
# 进入支付服务目录
cd congomall-pay/congomall-pay-interface

# 启动支付服务
mvn spring-boot:run
```

**注意**：保持这个终端窗口开启，服务将在端口8007运行。

### 2.3 验证服务启动（新开终端窗口）
```bash
# 检查服务是否启动
curl -s http://localhost:8007/api/pay-service
```

应该返回类似：`{"code":"B000001","message":"系统执行出错","data":null,"requestId":"","success":false}`

## 第三步：执行支付测试

### 3.1 创建测试脚本
创建文件 `test_payment.sh`：
```bash
cat > test_payment.sh << 'EOF'
#!/bin/bash

echo "=== 开始支付测试 ==="

# 1. 发起支付请求
echo "1. 发起支付请求..."
PAYMENT_RESPONSE=$(curl -s -X POST http://localhost:8007/api/pay-service \
  -H "Content-Type: application/json" \
  -d '{
    "orderSn": "ORDER_'$(date +%Y%m%d_%H%M%S)'",
    "outOrderSn": "OUT_ORDER_'$(date +%Y%m%d_%H%M%S)'", 
    "channel": "ALI_PAY",
    "tradeType": "NATIVE",
    "totalAmount": "99.99",
    "subject": "测试商品支付"
  }')

echo "支付响应: $PAYMENT_RESPONSE"

# 提取订单请求ID
ORDER_REQUEST_ID=$(echo $PAYMENT_RESPONSE | grep -o '"out_trade_no":"[^"]*"' | cut -d'"' -f4)
echo "订单请求ID: $ORDER_REQUEST_ID"

if [ -z "$ORDER_REQUEST_ID" ]; then
    echo "错误：无法获取订单请求ID"
    exit 1
fi

# 2. 检查数据库中的支付记录
echo "2. 检查数据库中的支付记录..."
/usr/local/mysql/bin/mysql -u root -p123456 congomall_pay -e "
SELECT id, order_sn, out_order_sn, channel, total_amount, status, create_time 
FROM pay_info 
WHERE order_request_id='$ORDER_REQUEST_ID';"

# 3. 模拟支付回调
echo "3. 模拟支付回调..."
CALLBACK_RESPONSE=$(curl -s -X POST "http://localhost:8007/api/pay-service/callback/alipay" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "gmt_create=$(date '+%Y-%m-%d+%H%%3A%M%%3A%S')&charset=UTF8&gmt_payment=$(date '+%Y-%m-%d+%H%%3A%M%%3A%S')&notify_time=$(date '+%Y-%m-%d+%H%%3A%M%%3A%S')&subject=测试商品支付&sign=test_sign&buyer_id=****************&body=&invoice_amount=99.99&version=1.0&notify_id=$(date +%Y%m%d%H%M%S)000000000000000000&fund_bill_list=%5B%7B%22amount%22%3A%2299.99%22%2C%22fundChannel%22%3A%22ALIPAYACCOUNT%22%7D%5D&notify_type=trade_status_sync&out_trade_no=$ORDER_REQUEST_ID&total_amount=99.99&trade_status=TRADE_SUCCESS&trade_no=$(date +%Y%m%d%H%M%S)001446870501234567&auth_app_id=****************&receipt_amount=99.99&point_amount=0.00&app_id=****************&buyer_pay_amount=99.99&sign_type=RSA2&seller_id=****************")

echo "回调响应: $CALLBACK_RESPONSE"

# 4. 验证支付结果
echo "4. 验证支付结果..."
/usr/local/mysql/bin/mysql -u root -p123456 congomall_pay -e "
SELECT id, order_sn, channel, total_amount, trade_no, gmt_payment, pay_amount, status, update_time 
FROM pay_info 
WHERE order_request_id='$ORDER_REQUEST_ID';"

echo "=== 支付测试完成 ==="
EOF

chmod +x test_payment.sh
```

### 3.2 执行测试脚本
```bash
./test_payment.sh
```

## 第四步：验证结果

### 4.1 查看所有支付记录
```bash
/usr/local/mysql/bin/mysql -u root -p123456 congomall_pay -e "
SELECT 
  id,
  order_sn,
  channel,
  total_amount,
  status,
  create_time,
  update_time
FROM pay_info 
ORDER BY create_time DESC 
LIMIT 5;"
```

### 4.2 查看支付服务日志
在支付服务运行的终端窗口中，您应该能看到类似以下的日志：
```
发起支付宝支付，订单号：ORDER_xxx，子订单号：OUT_ORDER_xxx，订单请求号：xxx，订单金额：99.99
```

## 第五步：创建便捷测试脚本

### 5.1 创建快速支付测试脚本
```bash
cat > quick_pay_test.sh << 'EOF'
#!/bin/bash

# 快速支付测试脚本
echo "快速支付测试开始..."

# 生成唯一订单号
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
ORDER_SN="ORDER_$TIMESTAMP"
OUT_ORDER_SN="OUT_ORDER_$TIMESTAMP"

echo "订单号: $ORDER_SN"
echo "子订单号: $OUT_ORDER_SN"

# 发起支付
curl -X POST http://localhost:8007/api/pay-service \
  -H "Content-Type: application/json" \
  -d "{
    \"orderSn\": \"$ORDER_SN\",
    \"outOrderSn\": \"$OUT_ORDER_SN\", 
    \"channel\": \"ALI_PAY\",
    \"tradeType\": \"NATIVE\",
    \"totalAmount\": \"88.88\",
    \"subject\": \"快速测试商品\"
  }" | jq '.'

echo "支付请求已发送，请检查数据库记录。"
EOF

chmod +x quick_pay_test.sh
```

### 5.2 创建数据库查询脚本
```bash
cat > check_payments.sh << 'EOF'
#!/bin/bash

echo "=== 最近的支付记录 ==="
/usr/local/mysql/bin/mysql -u root -p123456 congomall_pay -e "
SELECT 
  CONCAT('ID: ', id) as 支付ID,
  CONCAT('订单: ', order_sn) as 订单号,
  CONCAT('金额: ¥', total_amount) as 金额,
  CONCAT('状态: ', status) as 状态,
  CONCAT('时间: ', create_time) as 创建时间
FROM pay_info 
ORDER BY create_time DESC 
LIMIT 10;"
EOF

chmod +x check_payments.sh
```

## 使用说明

1. **首次运行**：按照第一步到第三步的顺序执行
2. **后续测试**：直接运行 `./quick_pay_test.sh` 进行快速测试
3. **查看记录**：运行 `./check_payments.sh` 查看支付记录
4. **完整测试**：运行 `./test_payment.sh` 进行完整的支付+回调测试

## 故障排除

### 如果支付服务启动失败：
```bash
# 检查端口占用
lsof -i :8007

# 如果端口被占用，杀死进程
kill -9 <PID>
```

### 如果数据库连接失败：
```bash
# 重启MySQL
sudo /usr/local/mysql/support-files/mysql.server restart

# 检查数据库状态
/usr/local/mysql/bin/mysql -u root -p123456 -e "SELECT 1;"
```

### 如果Redis连接失败：
```bash
# 启动Redis
redis-server --daemonize yes

# 测试Redis连接
redis-cli ping
```

## 预期结果

成功执行后，您应该看到：
1. 支付服务正常启动并监听8007端口
2. 支付请求返回包含支付宝表单的JSON响应
3. 数据库中创建了支付记录，状态为 `WAIT_BUYER_PAY`
4. 回调处理后，支付状态更新为 `TRADE_SUCCESS`
5. 所有支付信息（金额、时间、交易号等）正确保存
