#!/bin/bash

echo "=== 启动支付服务相关组件 ==="

# 1. 检查并启动MySQL
echo "1. 检查MySQL服务..."
if ! ps aux | grep -v grep | grep mysql > /dev/null; then
    echo "启动MySQL服务..."
    sudo /usr/local/mysql/support-files/mysql.server start
else
    echo "MySQL服务已运行"
fi

# 2. 检查并启动Redis
echo "2. 检查Redis服务..."
if ! ps aux | grep -v grep | grep redis-server > /dev/null; then
    echo "启动Redis服务..."
    redis-server --daemonize yes
else
    echo "Redis服务已运行"
fi

# 3. 验证数据库连接
echo "3. 验证数据库连接..."
if /usr/local/mysql/bin/mysql -u root -p123456 -e "SELECT 1;" > /dev/null 2>&1; then
    echo "数据库连接正常"
else
    echo "数据库连接失败，请检查MySQL服务和密码"
    exit 1
fi

# 4. 检查支付数据库
echo "4. 检查支付数据库..."
/usr/local/mysql/bin/mysql -u root -p123456 -e "USE congomall_pay; SHOW TABLES;"

echo "=== 基础服务启动完成 ==="
echo "现在可以启动支付服务了："
echo "cd congomall-pay/congomall-pay-interface && mvn spring-boot:run"
