# 支付服务测试使用说明

## 📁 文件说明

本目录包含以下测试文件：

1. **payment_step_by_step_guide.md** - 详细的操作指南
2. **test_payment.sh** - 完整支付流程测试脚本
3. **quick_pay_test.sh** - 快速支付测试脚本
4. **check_payments.sh** - 查看支付记录脚本
5. **start_services.sh** - 启动基础服务脚本

## 🚀 快速开始

### 第一次使用（完整流程）

1. **启动基础服务**
```bash
./start_services.sh
```

2. **启动支付服务**（新开终端窗口）
```bash
cd congomall-pay/congomall-pay-interface
mvn spring-boot:run
```

3. **执行完整测试**（再开一个新终端）
```bash
./test_payment.sh
```

### 日常测试（快速流程）

1. **确保支付服务运行**
```bash
curl -s http://localhost:8007/api/pay-service
```

2. **快速支付测试**
```bash
./quick_pay_test.sh
```

3. **查看支付记录**
```bash
./check_payments.sh
```

## 📋 测试步骤详解

### 1. 启动基础服务
- 检查并启动MySQL
- 检查并启动Redis  
- 验证数据库连接
- 检查支付数据库表

### 2. 启动支付服务
- 编译项目：`mvn clean compile -DskipTests`
- 启动服务：`mvn spring-boot:run`
- 服务运行在端口：8007

### 3. 执行支付测试
- 发起支付请求（99.99元）
- 检查数据库记录（状态：WAIT_BUYER_PAY）
- 模拟支付回调（状态：TRADE_SUCCESS）
- 验证最终结果

## 🔍 预期结果

### 支付请求成功
```json
{
  "code": "0",
  "message": null,
  "data": {
    "body": "<form name=\"punchout_form\" method=\"post\" action=\"https://openapi.alipaydev.com/gateway.do?...\">"
  },
  "success": true
}
```

### 数据库记录
```
+---------------------+--------------------+------------------------+---------+------------+--------------------+---------------------+--------------+------------------------------+---------------------+------------+---------------+
| id                  | order_sn           | out_order_sn           | channel | trade_type | subject            | order_request_id    | total_amount | trade_no                     | gmt_payment         | pay_amount | status        |
+---------------------+--------------------+------------------------+---------+------------+--------------------+---------------------+--------------+------------------------------+---------------------+------------+---------------+
| 1964955993343225856 | ORDER_20250908_001 | OUT_ORDER_20250908_001 | ALI_PAY | NATIVE     | 测试商品支付       | 1964955991816499200 |        99.99 | 2025090822001446870501234567 | 2025-09-08 15:37:00 |      99.99 | TRADE_SUCCESS |
+---------------------+--------------------+------------------------+---------+------------+--------------------+---------------------+--------------+------------------------------+---------------------+------------+---------------+
```

## ⚠️ 故障排除

### 支付服务启动失败
```bash
# 检查端口占用
lsof -i :8007
# 杀死占用进程
kill -9 <PID>
```

### 数据库连接失败
```bash
# 重启MySQL
sudo /usr/local/mysql/support-files/mysql.server restart
# 测试连接
/usr/local/mysql/bin/mysql -u root -p123456 -e "SELECT 1;"
```

### Redis连接失败
```bash
# 启动Redis
redis-server --daemonize yes
# 测试连接
redis-cli ping
```

## 📝 测试记录

每次测试后，系统会自动：
1. 生成唯一的订单号（基于时间戳）
2. 创建支付记录到数据库
3. 模拟支付宝回调处理
4. 更新支付状态为成功

## 🎯 测试目标

通过这些脚本，您可以验证：
- ✅ 支付服务正常启动
- ✅ 支付接口正常响应
- ✅ 数据库操作正确
- ✅ 支付回调处理正常
- ✅ 支付状态流转正确

## 📞 技术支持

如果遇到问题，请检查：
1. Java版本（需要Java 8+）
2. Maven版本
3. MySQL服务状态
4. Redis服务状态
5. 网络连接（支付宝沙箱环境）

祝您测试顺利！🎉
