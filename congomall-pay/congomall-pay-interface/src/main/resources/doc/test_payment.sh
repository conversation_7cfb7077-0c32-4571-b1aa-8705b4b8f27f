#!/bin/bash

echo "=== 开始支付测试 ==="

# 1. 发起支付请求
echo "1. 发起支付请求..."
PAYMENT_RESPONSE=$(curl -s -X POST http://localhost:8007/api/pay-service \
  -H "Content-Type: application/json" \
  -d '{
    "orderSn": "ORDER_'$(date +%Y%m%d_%H%M%S)'",
    "outOrderSn": "OUT_ORDER_'$(date +%Y%m%d_%H%M%S)'", 
    "channel": "ALI_PAY",
    "tradeType": "NATIVE",
    "totalAmount": "99.99",
    "subject": "测试商品支付"
  }')

echo "支付响应: $PAYMENT_RESPONSE"

# 提取订单请求ID
ORDER_REQUEST_ID=$(echo $PAYMENT_RESPONSE | grep -o '"out_trade_no":"[^"]*"' | cut -d'"' -f4)
echo "订单请求ID: $ORDER_REQUEST_ID"

if [ -z "$ORDER_REQUEST_ID" ]; then
    echo "错误：无法获取订单请求ID"
    exit 1
fi

# 2. 检查数据库中的支付记录
echo "2. 检查数据库中的支付记录..."
/usr/local/mysql/bin/mysql -u root -p123456 congomall_pay -e "
SELECT id, order_sn, out_order_sn, channel, total_amount, status, create_time 
FROM pay_info 
WHERE order_request_id='$ORDER_REQUEST_ID';"

# 3. 模拟支付回调
echo "3. 模拟支付回调..."
CALLBACK_RESPONSE=$(curl -s -X POST "http://localhost:8007/api/pay-service/callback/alipay" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "gmt_create=$(date '+%Y-%m-%d+%H%%3A%M%%3A%S')&charset=UTF8&gmt_payment=$(date '+%Y-%m-%d+%H%%3A%M%%3A%S')&notify_time=$(date '+%Y-%m-%d+%H%%3A%M%%3A%S')&subject=测试商品支付&sign=test_sign&buyer_id=****************&body=&invoice_amount=99.99&version=1.0&notify_id=$(date +%Y%m%d%H%M%S)000000000000000000&fund_bill_list=%5B%7B%22amount%22%3A%2299.99%22%2C%22fundChannel%22%3A%22ALIPAYACCOUNT%22%7D%5D&notify_type=trade_status_sync&out_trade_no=$ORDER_REQUEST_ID&total_amount=99.99&trade_status=TRADE_SUCCESS&trade_no=$(date +%Y%m%d%H%M%S)001446870501234567&auth_app_id=****************&receipt_amount=99.99&point_amount=0.00&app_id=****************&buyer_pay_amount=99.99&sign_type=RSA2&seller_id=****************")

echo "回调响应: $CALLBACK_RESPONSE"

# 4. 验证支付结果
echo "4. 验证支付结果..."
/usr/local/mysql/bin/mysql -u root -p123456 congomall_pay -e "
SELECT id, order_sn, channel, total_amount, trade_no, gmt_payment, pay_amount, status, update_time 
FROM pay_info 
WHERE order_request_id='$ORDER_REQUEST_ID';"

echo "=== 支付测试完成 ==="
