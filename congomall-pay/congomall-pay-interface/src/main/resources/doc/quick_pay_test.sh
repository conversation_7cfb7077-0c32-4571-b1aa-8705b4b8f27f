#!/bin/bash

# 快速支付测试脚本
echo "快速支付测试开始..."

# 生成唯一订单号
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
ORDER_SN="ORDER_$TIMESTAMP"
OUT_ORDER_SN="OUT_ORDER_$TIMESTAMP"

echo "订单号: $ORDER_SN"
echo "子订单号: $OUT_ORDER_SN"

# 发起支付
echo "发送支付请求..."
curl -X POST http://localhost:8007/api/pay-service \
  -H "Content-Type: application/json" \
  -d "{
    \"orderSn\": \"$ORDER_SN\",
    \"outOrderSn\": \"$OUT_ORDER_SN\", 
    \"channel\": \"ALI_PAY\",
    \"tradeType\": \"NATIVE\",
    \"totalAmount\": \"88.88\",
    \"subject\": \"快速测试商品\"
  }"

echo ""
echo "支付请求已发送，请检查数据库记录。"
